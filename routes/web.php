<?php

use App\Http\Controllers\AiPermissionController;
use App\Http\Controllers\ContentController;
use App\Http\Controllers\ImageUploadController;
use App\Http\Controllers\MediaController;
use App\Http\Controllers\WEB\Admin\Categories\Index as CategoryController;
use App\Http\Controllers\WEB\Admin\Contacts\Index as ContactController;
use App\Http\Controllers\WEB\Admin\PageController as CPageController;
use App\Http\Controllers\WEB\Admin\Pages\Home as Dashboard;
use App\Http\Controllers\WEB\Admin\Pages\Index as PageController;
use App\Http\Controllers\WEB\Admin\Profile;
use App\Http\Controllers\WEB\Admin\Roles\Index as RolesController;
use App\Http\Controllers\WEB\Admin\Schools\Index as SchoolController;
use App\Http\Controllers\WEB\Admin\Settings\Index as SettingsController;
use App\Http\Controllers\WEB\Admin\Users\Index as UserController;
use App\Http\Controllers\WEB\Admin\Users\Students as StudentsController;
use App\Http\Controllers\WEB\Admin\Users\Teachers as TeachersController;
use App\Http\Controllers\WEB\Public\Page as PublicPageController;
use App\Models\Language;
use App\Models\Page;
use App\Models\Role;
use App\Models\Setting;
use App\Models\User;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Session;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/
Route::get('/change-languages/{short}', function ($short) {
    $languages = Language::where('status', 1)->get();

    if (in_array($short, $languages->pluck('short')->toArray())) {
        Session::forget('lang');
        Session::put('lang', $short);
        App::setLocale(strtolower(Session::get('lang')));
    }

    return redirect()->back();
})->name('change.languages');
Route::get('/', function () {
    return redirect()->route('dashboard');
});
Route::get('/', function () {
    return response()->file(public_path('home/index.html'));
});
Route::get('/media/{id}', [MediaController::class, 'show'])->name('media.show')->middleware('auth');
Route::get('/file/{id}.{extension?}', [MediaController::class, 'show'])->name('api.media.show');
Route::get('/files/{id}', [MediaController::class, 'web_show'])->name('web.show');
Route::middleware(['auth:sanctum', config('jetstream.auth_session'), 'verified'])->group(function () {
    Route::get('/dashboard', Dashboard::class)->name('dashboard')->can('viewAny', Setting::class);
    Route::get('/settings', SettingsController::class)->name('settings')->can('viewAny', Setting::class);
    Route::get('/profile', Profile::class)->name('profile');
    Route::get('/roles', RolesController::class)->name('roles')->can('viewAny', Role::class);
    Route::get('/pages', PageController::class)->name('pages'); // ->can('viewAny', Page::class);
    Route::get('/add-page', [CPageController::class, 'add'])->name('new-page'); // ->can('viewAny', Page::class);
    Route::get('/edit-page/{id}', [CPageController::class, 'edit'])->name('edit-page'); // ->can('viewAny', Page::class);
    Route::post('/save-page', [CPageController::class, 'store'])->name('save-page'); // ->can('viewAny', Page::class);
    Route::post('/update-page', [CPageController::class, 'update'])->name('update-page');
    Route::get('/users', UserController::class)->name('users')->can('viewAny', User::class);
    Route::get('/schools', SchoolController::class)->name('schools')->can('viewAny', Role::class);
    Route::get('/teachers', TeachersController::class)->name('teachers')->can('viewAny', User::class);
    Route::get('/students', StudentsController::class)->name('students')->can('viewAny', User::class);
    Route::get('/categories', CategoryController::class)->name('categories')->can('viewAny', User::class);
    Route::get('/contacts', ContactController::class)->name('contacts')->can('viewAny', User::class);
    // Route::get('/add-content', ContentCreateController::class)->name('add.content')->can('viewAny', User::class);
    Route::get('/add-content/{category}', [ContentController::class, 'add'])->name('add.content')->can('viewAny', User::class);
    Route::post('/content-store', [ContentController::class, 'store'])->name('content.store')->can('viewAny', User::class);
    Route::post('/content-update', [ContentController::class, 'update'])->name('content.update');
    Route::post('/upload', [ContentController::class, 'upload'])->name('file.upload')->can('viewAny', User::class);
    Route::post('/upload-new-media', [CPageController::class, 'upload'])->name('new.file.upload')->can('viewAny', User::class);
    Route::post('/delete-media', [ContentController::class, 'deleteMedia'])->name('delete.media')->can('viewAny', User::class);
    Route::post('/delete.media.page', [CPageController::class, 'deletePageMedia'])->name('delete.page.media')->can('viewAny', User::class);
    Route::post('/upload-additional-media', [ContentController::class, 'uploadAdditionalMedia'])->name('upload.additional.media');
    Route::middleware(['auth'])->group(function () {
        Route::post('/analyze-content', [\App\Http\Controllers\AIAnalysisController::class, 'analyzeContent'])->name('analyze.content');

        // Teacher Questions Routes
        Route::prefix('teacher-questions')->group(function () {
            Route::post('/add', [\App\Http\Controllers\QuestionController::class, 'store'])->name('teacher.add.question');
            Route::post('/update', [\App\Http\Controllers\QuestionController::class, 'update'])->name('teacher.update.question');
            Route::post('/delete', [\App\Http\Controllers\QuestionController::class, 'destroy'])->name('teacher.delete.question');
        });

        // AI Questions Routes
        Route::prefix('ai-questions')->group(function () {
            Route::post('/add', [\App\Http\Controllers\AIAnalysisController::class, 'addQuestion'])->name('ai.add.question');
            Route::post('/save', [\App\Http\Controllers\AIAnalysisController::class, 'saveQuestion']);
            Route::post('/update', [\App\Http\Controllers\AIAnalysisController::class, 'updateQuestion']);
            Route::post('/delete', [\App\Http\Controllers\AIAnalysisController::class, 'deleteQuestion']);
            Route::get('/{contentId}', [\App\Http\Controllers\AIAnalysisController::class, 'getQuestions'])->name('ai.get.questions');
            Route::post('/generate-suggestions', [\App\Http\Controllers\AIAnalysisController::class, 'generateSuggestions'])->name('ai.generate.suggestions');
        });

        // AI Questions Suggestion Route
        Route::post('/suggest-questions', [\App\Http\Controllers\AIAnalysisController::class, 'suggestQuestions'])->name('suggest.questions');

        // AI Answer Generation Route
        Route::post('/generate-ai-answer', [\App\Http\Controllers\AIAnalysisController::class, 'generateAIAnswer'])->name('generate.ai.answer');
    });
    Route::post('/upload-editor-image', [ImageUploadController::class, 'upload'])->name('upload.editor.image');
    Route::get('/content/{id}/questions', [ContentController::class, 'showQuestions'])->name('content.questions');
    Route::get('/content/{id}/analysis', [ContentController::class, 'showAnalysis'])->name('content.analysis');
    Route::get('/ai/usage-stats/{teacherId}', function($teacherId) {
        $today = \Carbon\Carbon::today();
        $stats = \App\Models\AiUsageLog::where('teacher_id', $teacherId)
            ->where('usage_date', $today)
            ->get()
            ->groupBy('action')
            ->map(function($group) {
                return $group->sum('count');
            });
        return response()->json($stats);
    })->name('ai.usage.stats');
}); // public routes
Route::get('/page/{slug}', PublicPageController::class)->name('public.page');
Route::get('/download', function () {
    $filePath = public_path('downloads/dawraq.apk');
    if (file_exists($filePath)) {
        return response()->download($filePath, 'dawraq.apk', [
            'Content-Type' => 'application/vnd.android.package-archive',
        ]);
    }
    abort(404);
})->name('download');
Route::group(['middleware' => ['auth']], function () {
    Route::resource('ai/permissions', AiPermissionController::class)->names([
        'index' => 'ai.permissions.index',
        'create' => 'ai.permissions.create',
        'store' => 'ai.permissions.store',
        'show' => 'ai.permissions.show',
        'edit' => 'ai.permissions.edit',
        'update' => 'ai.permissions.update',
        'destroy' => 'ai.permissions.destroy',
    ]);
    Route::get('/ai/check-permission/{teacherId}/{action}', [\App\Http\Controllers\AiPermissionController::class, 'checkPermission'])->name('ai.check.permission');
    Route::post('/ai/log-usage', [\App\Http\Controllers\AiPermissionController::class, 'logUsage'])->name('ai.log.usage');

    // Admin Question Management Routes
    Route::prefix('admin/questions')->name('admin.questions.')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\QuestionManagementController::class, 'index'])->name('index');
        Route::post('/activate', [\App\Http\Controllers\Admin\QuestionManagementController::class, 'activate'])->name('activate');
        Route::post('/deactivate', [\App\Http\Controllers\Admin\QuestionManagementController::class, 'deactivate'])->name('deactivate');
        Route::post('/bulk-activate', [\App\Http\Controllers\Admin\QuestionManagementController::class, 'bulkActivate'])->name('bulk.activate');
        Route::post('/bulk-deactivate', [\App\Http\Controllers\Admin\QuestionManagementController::class, 'bulkDeactivate'])->name('bulk.deactivate');
        Route::delete('/destroy', [\App\Http\Controllers\Admin\QuestionManagementController::class, 'destroy'])->name('destroy');
        Route::get('/show/{id}', [\App\Http\Controllers\Admin\QuestionManagementController::class, 'show'])->name('show');
        Route::get('/statistics', [\App\Http\Controllers\Admin\QuestionManagementController::class, 'statistics'])->name('statistics');
    });
});
