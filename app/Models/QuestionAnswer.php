<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class QuestionAnswer extends Model
{
    use HasFactory;

    protected $table = 'questions_answers';

    protected $fillable = [
        'user_id',
        'content_id',
        'question',
        'answer',
        'difficulty',
        'type',
        'status',
        'is_ai_generated'
    ];

    protected $casts = [
        'status' => 'boolean',
        'is_ai_generated' => 'boolean'
    ];

    public function content()
    {
        return $this->belongsTo(Content::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope للأسئلة المفعلة فقط
     */
    public function scopeActive($query)
    {
        return $query->where('status', 1);
    }

    /**
     * Scope للأسئلة غير المفعلة
     */
    public function scopeInactive($query)
    {
        return $query->where('status', 0);
    }

    /**
     * Scope للأسئلة الخاصة بمعلم معين
     */
    public function scopeByTeacher($query, $teacherId)
    {
        return $query->where('user_id', $teacherId);
    }
}
