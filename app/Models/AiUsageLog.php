<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class AiUsageLog extends Model
{
    protected $fillable = [
        'teacher_id',
        'action',
        'usage_date',
        'count'
    ];

    protected $casts = [
        'usage_date' => 'date'
    ];

    public function teacher()
    {
        return $this->belongsTo(User::class, 'teacher_id');
    }

    public static function logUsage($teacherId, $action)
    {
        $today = Carbon::today();
        
        $log = self::firstOrCreate(
            [
                'teacher_id' => $teacherId,
                'action' => $action,
                'usage_date' => $today
            ],
            ['count' => 0]
        );
        
        $log->increment('count');
        return $log;
    }

    public static function getTodayUsage($teacherId, $action)
    {
        return self::where('teacher_id', $teacherId)
            ->where('action', $action)
            ->where('usage_date', Carbon::today())
            ->value('count') ?? 0;
    }

    public static function canPerformAction($teacherId, $action, $limit)
    {
        if ($limit == 0) return true; // unlimited
        
        $todayUsage = self::getTodayUsage($teacherId, $action);
        return $todayUsage < $limit;
    }
}