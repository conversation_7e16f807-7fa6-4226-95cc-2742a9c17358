<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\Content;
use App\Services\CategoryService;
use App\Services\ContentService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ContentController extends Controller
{
    public function __construct(protected ContentService $ContentService, protected CategoryService $CategoryService)
    {
    }

    public function index()
    {
    }

    // public function add($id)
    // {
    //     $cat = Category::find($id);
    //     $cont = Content::where('category_id', $id)->first();
    //     // dd($cont->description);
    //     if ($cat) {
    //         if ($cont) {
    //             $data = $cont;
    //             $contentData = json_decode($cont->description, true);

    //             $content = [
    //                 // 'class' => htmlspecialchars($cont->description['class'] ?? '', ENT_QUOTES, 'UTF-8'),
    //                 // 'teacher' => htmlspecialchars($cont->description['teacher'] ?? '', ENT_QUOTES, 'UTF-8'),
    //                 // 'student' => htmlspecialchars($cont->description['student'] ?? '', ENT_QUOTES, 'UTF-8'),
    //                 'class' => $cont->description['class'],
    //                 'teacher' => $cont->description['teacher'],
    //                 'student' => $cont->description['student'],
    //             ];

    //             return view('pages.update', compact('id', 'data', 'content'));
    //         }

    //         return view('pages.create', compact('id'));
    //     } else {
    //         return redirect()->back()->with('error', 'not found!');
    //     }
    // }
    public function add($id)
    {
        $cat = Category::find($id);
        $cont = Content::where('category_id', $id)->first();

        if ($cat) {
            if ($cont) {
                $data = $cont->load(['aiAnalysis', 'questionsAnswers.user']);
                $contentData = json_decode($cont->description, true);
                $content = [
                    'class' => $contentData['class'] ?? '',
                    'teacher' => $contentData['teacher'] ?? '',
                    'student' => $contentData['student'] ?? '',
                ];
                $hasAnalysis = $data->aiAnalysis ? true : false;

                // فحص صلاحيات الذكاء الاصطناعي
                $user = auth()->user();
                $canUseAI = false;

                if ($user->role_id === 1) {
                    $canUseAI = true;
                } elseif ($user->type === 2) {
                    $permissions = \App\Models\AiPermission::where(function ($query) use ($user) {
                        $query->where('teacher_id', $user->id)
                            ->orWhere('school_id', $user->school_id);
                    })->first();

                    $canUseAI = $permissions && ($permissions->can_view_analysis || $permissions->can_generate_questions);
                }

                return view('pages.update', compact('id', 'data', 'content', 'hasAnalysis', 'canUseAI'));
            }

            return view('pages.create', compact('id'));
        } else {
            return redirect()->back()->with('error', 'Category not found!');
        }
    }

    public function store(Request $request)
    {
        $request->validate([
            'category' => 'required|exists:categories,id',
            'content_class' => 'nullable',
            'content_teacher' => 'nullable',
            'content_student' => 'nullable',
        ]);

        $data = [
            'category_id' => $request->category,
            'description' => json_encode([
                'class' => $request->content_class,
                'teacher' => $request->content_teacher,
                'student' => $request->content_student,
            ]),
        ];

        $created = $this->ContentService->createContent($data);
        if ($created->isSuccess) {
            return redirect()->route('categories')->with('success', 'Content created successfully!');
        } else {
            return redirect()->route('categories')->with('error', 'Failed to create content!');
        }
    }

    public function update(Request $request)
    {
        $content = Content::with(['aiAnalysis', 'questionsAnswers.user'])->find($request->id);

        if ($content) {
            $request->validate([
                'category' => 'required|exists:categories,id',
                'content_class' => 'nullable',
                'content_teacher' => 'nullable',
                'content_student' => 'nullable',
            ]);

            $data = [
                'category_id' => $request->category,
                'description' => json_encode([
                    'class' => $request->content_class,
                    'teacher' => $request->content_teacher,
                    'student' => $request->content_student,
                ]),
            ];

            $updated = $this->ContentService->updateContent($request->id, $data);

            if ($updated->isSuccess) {
                // Reload the content with relationships after update
                $content = Content::with(['aiAnalysis', 'questionsAnswers.user'])->find($request->id);
                $contentData = json_decode($content->description, true);
                $hasAnalysis = $content->aiAnalysis ? true : false;

                return view('pages.update', [
                    'id' => $content->category_id,
                    'data' => $content,
                    'content' => [
                        'class' => $contentData['class'] ?? '',
                        'teacher' => $contentData['teacher'] ?? '',
                        'student' => $contentData['student'] ?? '',
                    ],
                    'hasAnalysis' => $hasAnalysis
                ])->with('success', 'Content updated successfully!');
            } else {
                return redirect()->back()->with('error', 'Failed to update content!');
            }
        } else {
            return redirect()->route('categories')->with('error', 'Content not found!');
        }
    }

    public function upload(Request $request)
    {
        try {
            // التحقق من صحة المدخلات
            $request->validate([
                'category' => 'required|exists:categories,id',
                'file' => 'required', // تأكد من أن الملف موجود وصالح
                'media_type' => 'required|in:class_media,teacher_media,student_media',
            ]);

            // البحث عن الفئة
            $cat = Category::find($request->category);

            if (!$cat) {
                return response()->json(['success' => false, 'message' => 'Category not found'], 404);
            }

            // البحث عن أول محتوى مرتبط بالفئة
            $content = $cat->contents()->first();

            // إذا لم يوجد محتوى، قم بإنشاء محتوى جديد
            if (!$content) {
                $content = new Content(); // تأكد من استخدام النموذج الصحيح هنا
                $content->category_id = $cat->id;
                $content->save();
            }

            // تحقق من وجود الملف في الطلب
            if ($request->file('file')) {
                $file = $request->file('file');
                // if ($content->hasMedia($request->media_type)) {
                //     $content->clearMediaCollection($request->media_type);
                // }
                $media = $content->addMedia($file)->toMediaCollection($request->media_type, 'private_media');

                return response()->json([
                    'success' => true,
                    'file_path' => $media->getUrl(),
                ], 200);
            }
        } catch (\Exception $e) {
            Log::error('Upload error: '.$e->getMessage(), ['exception' => $e]);
        }

        return response()->json(['success' => true, 'message' => __('Media uploaded successfully')], 200);
    }

    // public function deleteMedia(Request $request)
    // {
    //     try {
    //         $content = Content::find($request->id);

    //         if ($content) {
    //             $mediaType = $request->media_type;

    //             if ($content->hasMedia($mediaType)) {
    //                 $content->clearMediaCollection($mediaType);

    //                 return response()->json(['success' => true, 'message' => 'Media deleted successfully'], 200);
    //             } else {
    //                 return response()->json(['success' => false, 'message' => 'Media not found'], 404);
    //             }
    //         } else {
    //             return response()->json(['success' => false, 'message' => 'Content not found'], 404);
    //         }
    //     } catch (\Exception $e) {
    //         Log::error('Delete media error: '.$e->getMessage(), ['exception' => $e]);

    //         return response()->json(['success' => false, 'message' => 'Error occurred while deleting media'], 500);
    //     }
    // }

    // public function uploadAdditionalMedia(Request $request)
    // {
    //     try {
    //         $request->validate([
    //             'file' => 'required|file|mimes:jpeg,png,jpg,gif,mp4,mov,avi|max:102400',
    //             'media_type' => 'required|string',
    //             'content_id' => 'required|integer',
    //         ]);

    //         $content = Content::findOrFail($request->content_id);

    //         if ($request->hasFile('file')) {
    //             $file = $request->file('file');

    //             // تحميل الملف واضافته للمجموعة المناسبة
    //             $media = $content->addMedia($file)
    //                            ->toMediaCollection($request->media_type);

    //             return response()->json([
    //                 'success' => true,
    //                 'message' => __('Media uploaded successfully'),
    //                 'media' => [
    //                     'id' => $media->id,
    //                     'url' => $media->getUrl(),
    //                     'mime_type' => $media->mime_type,
    //                 ],
    //             ]);
    //         }

    //         return response()->json([
    //             'success' => false,
    //             'message' => __('No file uploaded'),
    //         ], 400);
    //     } catch (\Exception $e) {
    //         \Log::error('Error uploading additional media: '.$e->getMessage());

    //         return response()->json([
    //             'success' => false,
    //             'message' => __('Error uploading media: ').$e->getMessage(),
    //         ], 500);
    //     }
    // }

    // تعديل دالة حذف الميديا لتتعامل مع الميديا المتعددة
    public function deleteMedia(Request $request)
    {
        // dd($request);
        try {
            $request->validate([
                'id' => 'required',
                'media_type' => 'required|string',
            ]);

            $content = Content::findOrFail($request->id);

            // حذف الميديا المحددة من المجموعة
            $media = $content->getMedia($request->media_type);
            foreach ($media as $mediaItem) {
                $mediaItem->where('id', $request->media_id)->delete();
            }

            return response()->json([
                'success' => true,
                'message' => __('Media deleted successfully'),
            ]);
        } catch (\Exception $e) {
            \Log::error('Error deleting media: '.$e->getMessage());

            return response()->json([
                'success' => false,
                'message' => __('Error deleting media: ').$e->getMessage(),
            ], 500);
        }
    }

    // اضافة الدالة التالية للتحقق من وجود ميديا
    public function hasMedia(Request $request)
    {
        try {
            $request->validate([
                'content_id' => 'required|integer',
                'media_type' => 'required|string',
            ]);

            $content = Content::findOrFail($request->content_id);
            $hasMedia = $content->hasMedia($request->media_type);
            $mediaCount = $content->getMedia($request->media_type)->count();

            return response()->json([
                'success' => true,
                'has_media' => $hasMedia,
                'media_count' => $mediaCount,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }
}
