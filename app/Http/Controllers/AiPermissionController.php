<?php

namespace App\Http\Controllers;

use App\Models\AiPermission;
use App\Models\AiUsageLog;
use App\Models\School;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AiPermissionController extends Controller
{
    public function index()
    {
        $permissions = AiPermission::with(['school', 'teacher'])->get();
        $usedTeacherIds = $permissions->pluck('teacher_id')->filter()->toArray();
        
        $schools = School::all();
        $teachers = User::where('type', 2)
                       ->whereNotIn('id', $usedTeacherIds)
                       ->get();

        return view('ai.permissions.index', compact('permissions', 'schools', 'teachers'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'school_id' => 'nullable|exists:schools,id',
            'teacher_id' => 'nullable|exists:users,id|required_if:school_id,null',
            'can_view_analysis' => 'nullable|boolean',
            'can_view_questions' => 'nullable|boolean',
            'can_add_questions' => 'nullable|boolean',
            'can_edit_questions' => 'nullable|boolean',
            'can_generate_questions' => 'nullable|boolean',
            'can_generate_answers' => 'nullable|boolean',
            'questions_limit' => 'required|integer|min:0',
        ]);
        
        // تعيين القيم الافتراضية للـ checkboxes
        $validated['can_view_analysis'] = $request->has('can_view_analysis') ? 1 : 0;
        $validated['can_view_questions'] = $request->has('can_view_questions') ? 1 : 0;
        $validated['can_add_questions'] = $request->has('can_add_questions') ? 1 : 0;
        $validated['can_edit_questions'] = $request->has('can_edit_questions') ? 1 : 0;
        $validated['can_generate_questions'] = $request->has('can_generate_questions') ? 1 : 0;
        $validated['can_generate_answers'] = $request->has('can_generate_answers') ? 1 : 0;
        $validated['created_by'] = auth()->id();

        // إذا تم اختيار مدرسة، إعطاء الصلاحيات لجميع معلميها
        if ($validated['school_id']) {
            return $this->assignPermissionsToSchoolTeachers($validated);
        }
        
        // إعطاء صلاحية لمعلم واحد
        if ($validated['teacher_id']) {
            $user = User::find($validated['teacher_id']);
            if ($user->type != 2) {
                return back()->with('error', 'المستخدم المحدد ليس معلماً');
            }
            
            AiPermission::create($validated);
            return redirect()->route('ai.permissions.index')->with('success', 'تم إضافة الصلاحية بنجاح');
        }
        
        return back()->with('error', 'يجب اختيار مدرسة أو معلم');
    }
    
    private function assignPermissionsToSchoolTeachers($permissionData)
    {
        $school = School::find($permissionData['school_id']);
        $teachers = User::where('type', 2)->where('school_id', $school->id)->get();
        
        if ($teachers->isEmpty()) {
            return back()->with('error', 'لا يوجد معلمون في هذه المدرسة');
        }
        
        $createdCount = 0;
        $updatedCount = 0;
        
        DB::transaction(function () use ($teachers, $permissionData, &$createdCount, &$updatedCount) {
            foreach ($teachers as $teacher) {
                $teacherPermission = $permissionData;
                $teacherPermission['teacher_id'] = $teacher->id;
                unset($teacherPermission['school_id']);
                
                $existingPermission = AiPermission::where('teacher_id', $teacher->id)->first();
                
                if ($existingPermission) {
                    $existingPermission->update($teacherPermission);
                    $updatedCount++;
                } else {
                    AiPermission::create($teacherPermission);
                    $createdCount++;
                }
            }
        });
        
        $message = "";
        if ($createdCount > 0) {
            $message .= "تم إضافة صلاحيات لـ {$createdCount} معلم";
        }
        if ($updatedCount > 0) {
            if ($createdCount > 0) $message .= " و";
            $message .= "تم تحديث صلاحيات {$updatedCount} معلم";
        }
        
        return redirect()->route('ai.permissions.index')->with('success', $message);
    }

    public function update(Request $request, AiPermission $permission)
    {
        $validated = $request->validate([
            'school_id' => 'nullable|exists:schools,id',
            'teacher_id' => 'nullable|exists:users,id|required_if:school_id,null',
            'can_view_analysis' => 'nullable|boolean',
            'can_view_questions' => 'nullable|boolean',
            'can_add_questions' => 'nullable|boolean',
            'can_edit_questions' => 'nullable|boolean',
            'can_generate_questions' => 'nullable|boolean',
            'can_generate_answers' => 'nullable|boolean',
            'questions_limit' => 'required|integer|min:0',
        ]);
        
        // تعيين القيم الافتراضية للـ checkboxes
        $validated['can_view_analysis'] = $request->input('can_view_analysis') == '1' ? 1 : 0;
        $validated['can_view_questions'] = $request->input('can_view_questions') == '1' ? 1 : 0;
        $validated['can_add_questions'] = $request->input('can_add_questions') == '1' ? 1 : 0;
        $validated['can_edit_questions'] = $request->input('can_edit_questions') == '1' ? 1 : 0;
        $validated['can_generate_questions'] = $request->input('can_generate_questions') == '1' ? 1 : 0;
        $validated['can_generate_answers'] = $request->input('can_generate_answers') == '1' ? 1 : 0;

        // التحقق من عدم وجود تكرار للمدرسة أو المعلم
        $exists = AiPermission::where('id', '!=', $permission->id)
            ->where(function ($query) use ($validated) {
                if ($validated['school_id']) {
                    $query->where('school_id', $validated['school_id']);
                }
                if ($validated['teacher_id']) {
                    $query->orWhere('teacher_id', $validated['teacher_id']);
                }
            })->exists();

        if ($exists && ($validated['school_id'] || $validated['teacher_id'])) {
            return back()->with('error', 'هذا المعلم أو المدرسة لديهم صلاحيات بالفعل');
        }

        if ($validated['teacher_id']) {
            $user = User::find($validated['teacher_id']);
            if ($user->type != 2) {
                return back()->with('error', 'المستخدم المحدد ليس معلماً');
            }
        }

        \Log::info('AI Permission Update - Before:', [
            'permission_id' => $permission->id,
            'old_values' => $permission->toArray()
        ]);
        
        \Log::info('AI Permission Update - Data to save:', $validated);
        
        $result = $permission->update($validated);
        
        \Log::info('AI Permission Update - After:', [
            'update_result' => $result,
            'new_values' => $permission->fresh()->toArray()
        ]);

        return redirect()->route('ai.permissions.index')->with('success', 'تم تحديث الصلاحية بنجاح');
    }

    public function destroy(AiPermission $permission)
    {
        $permission->delete();

        return redirect()->route('ai.permissions.index')->with('success', 'تم حذف الصلاحية بنجاح');
    }
    
    public function checkPermission($teacherId, $action)
    {
        $permission = AiPermission::where('teacher_id', $teacherId)->first();
        
        if (!$permission) {
            return response()->json(['allowed' => false, 'message' => 'لا توجد صلاحيات']);
        }
        
        $actionField = 'can_' . $action;
        if (!$permission->$actionField) {
            return response()->json(['allowed' => false, 'message' => 'لا تملك صلاحية لهذا الإجراء']);
        }
        
        // فحص الحد اليومي
        if ($action === 'add_questions' && $permission->questions_limit > 0) {
            $canPerform = AiUsageLog::canPerformAction($teacherId, $action, $permission->questions_limit);
            $todayUsage = AiUsageLog::getTodayUsage($teacherId, $action);
            
            if (!$canPerform) {
                return response()->json([
                    'allowed' => false, 
                    'message' => "تم استنفاد الحد اليومي ({$todayUsage}/{$permission->questions_limit})"
                ]);
            }
            
            return response()->json([
                'allowed' => true, 
                'remaining' => $permission->questions_limit - $todayUsage,
                'usage' => $todayUsage,
                'limit' => $permission->questions_limit
            ]);
        }
        
        return response()->json(['allowed' => true]);
    }
    
    public function logUsage(Request $request)
    {
        $request->validate([
            'teacher_id' => 'required|exists:users,id',
            'action' => 'required|string'
        ]);
        
        AiUsageLog::logUsage($request->teacher_id, $request->action);
        
        return response()->json(['success' => true]);
    }
}
