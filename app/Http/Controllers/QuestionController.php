<?php

namespace App\Http\Controllers;

use App\Models\AiPermission;
use App\Models\AiUsageLog;
use App\Models\QuestionAnswer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class QuestionController extends Controller
{
    /**
     * Store a newly created question in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'content_id' => 'required|exists:contents,id',
            'question' => 'required|string|max:1000',
            'answer' => 'nullable|string|max:2000',
            // 'difficulty' => 'required|in:سهل,متوسط,صعب',
            // 'type' => 'nullable|string|max:100',
            // 'is_ai_generated' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first(),
            ], 422);
        }

        try {
            // فحص الصلاحيات والحد اليومي للمعلمين
            $user = Auth::user();
            if ($user->type === 2) { // معلم
                $permission = AiPermission::where(function ($query) use ($user) {
                    $query->where('teacher_id', $user->id)
                        ->orWhere('school_id', $user->school_id);
                })->first();

                if (!$permission || !$permission->can_add_questions) {
                    return response()->json([
                        'success' => false,
                        'message' => 'لا تملك صلاحية إضافة الأسئلة',
                    ], 403);
                }

                // فحص الحد اليومي
                if ($permission->questions_limit > 0) {
                    if (!AiUsageLog::canPerformAction($user->id, 'add_questions', $permission->questions_limit)) {
                        $todayUsage = AiUsageLog::getTodayUsage($user->id, 'add_questions');
                        return response()->json([
                            'success' => false,
                            'message' => "تم استنفاد الحد اليومي ({$todayUsage}/{$permission->questions_limit})",
                        ], 429);
                    }
                }
            }

            $question = QuestionAnswer::create([
                'user_id' => $user->id,
                'content_id' => $request->content_id,
                'question' => $request->question,
                'answer' => $request->answer,
                'difficulty' => 'متوسط',
                'type' => $request->type ?? 'عام',
                'status' => $user->type === 2 ? 0 : 1, // المعلمون: غير مفعل، الأدمن: مفعل
                'is_ai_generated' => false,
            ]);

            // تسجيل الاستخدام للمعلمين
            if ($user->type === 2) { // معلم
                AiUsageLog::logUsage($user->id, 'add_questions');
            }

            return response()->json([
                'success' => true,
                'message' => 'تم إضافة السؤال بنجاح',
                'question' => $question,
            ]);
        } catch (\Exception $e) {
            \Log::error('Error adding question: '.$e->getMessage());

            return response()->json([
                'success' => false,
                'message' => __('Failed to add question. Please try again.'),
            ], 500);
        }
    }

    /**
     * Update the specified question in storage.
     */
    public function update(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'question_id' => 'required|exists:questions_answers,id',
            'question' => 'required|string|max:1000',
            'answer' => 'required|string|max:2000',
            'difficulty' => 'required|in:سهل,متوسط,صعب',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first(),
            ], 422);
        }

        try {
            $question = QuestionAnswer::findOrFail($request->question_id);

            // Check if the user has permission to update this question
            //            if ($question->created_by != Auth::id()) {
            //                return response()->json([
            //                    'success' => false,
            //                    'message' => __('You are not authorized to update this question.')
            //                ], 403);
            //            }

            $question->update([
                'question' => $request->question,
                'answer' => $request->answer,
                'difficulty' => $request->difficulty,
            ]);

            return response()->json([
                'success' => true,
                'message' => __('Question updated successfully'),
                'data' => $question,
            ]);
        } catch (\Exception $e) {
            \Log::error('Error updating question: '.$e->getMessage());

            return response()->json([
                'success' => false,
                'message' => __('Failed to update question. Please try again.'),
            ], 500);
        }
    }

    /**
     * Remove the specified question from storage.
     */
    public function destroy(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'question_id' => 'required|exists:questions_answers,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first(),
            ], 422);
        }

        try {
            $question = QuestionAnswer::findOrFail($request->question_id);

            // Check if the user has permission to delete this question
            //            if ($question->created_by != Auth::id()) {
            //                return response()->json([
            //                    'success' => false,
            //                    'message' => __('You are not authorized to delete this question.')
            //                ], 403);
            //            }

            $question->delete();

            return response()->json([
                'success' => true,
                'message' => __('Question deleted successfully'),
            ]);
        } catch (\Exception $e) {
            \Log::error('Error deleting question: '.$e->getMessage());

            return response()->json([
                'success' => false,
                'message' => __('Failed to delete question. Please try again.'),
            ], 500);
        }
    }
}
