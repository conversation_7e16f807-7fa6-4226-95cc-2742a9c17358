<?php

namespace App\Http\Controllers;

use App\Models\AiAnalysis;
use App\Models\AiPermission;
use App\Models\AiUsageLog;
use App\Models\Content;
use App\Models\QuestionAnswer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class AIAnalysisController extends Controller
{
    private $aiModel = 'gemini';

    public function analyzeContent(Request $request)
    {
        try {
            $request->validate([
                'content_id' => 'required|numeric',
                'content' => 'required|string',
                'media_type' => 'required|string',
            ]);

            $teacherId = auth()->id();

            // فحص الصلاحيات
            $permission = AiPermission::where('teacher_id', $teacherId)->first();
            if (!$permission || !$permission->can_view_analysis) {
                return response()->json([
                    'success' => false,
                    'message' => 'لا تملك صلاحية عرض التحليل'
                ], 403);
            }

            $content = Content::findOrFail($request->content_id);
            $media = $content->getMedia($request->media_type);

            $geminiKey = env('GEMINI_API_KEY');
            if ($this->aiModel === 'gemini' && !empty($geminiKey)) {
                $analysis = $this->analyzeWithGemini($content, $media);

                return response()->json(['success' => true, 'analysis' => $analysis]);
            }

            throw new \Exception('لا يوجد مفتاح API صالح');
        } catch (\Exception $e) {
            \Log::error('AI Analysis Error: '.$e->getMessage());
            \Log::error('Stack trace: '.$e->getTraceAsString());

            // رسالة خطأ أكثر تفصيلاً للمطور
            $errorMessage = 'حدث خطأ أثناء تحليل المحتوى';
            if (app()->environment('local')) {
                $errorMessage .= ': '.$e->getMessage();
            }

            return response()->json([
                'success' => false,
                'message' => $errorMessage,
            ], 500);
        }
    }

    public function getQuestions($contentId)
    {
        $teacherId = auth()->id();

        // فحص الصلاحيات
        $permission = AiPermission::where('teacher_id', $teacherId)->first();
        if (!$permission || !$permission->can_view_questions) {
            return response()->json(['error' => 'لا تملك صلاحية عرض الأسئلة'], 403);
        }

        // جلب الأسئلة حسب نوع المستخدم
        $user = auth()->user();
        if ($user->type === 1) { // أدمن - يرى جميع الأسئلة
            $questions = QuestionAnswer::where('content_id', $contentId)->get();
        } else { // معلم - يرى أسئلته الخاصة + الأسئلة المفعلة من الآخرين
            $questions = QuestionAnswer::where('content_id', $contentId)
                ->where(function($query) use ($teacherId) {
                    $query->where('user_id', $teacherId) // أسئلته الخاصة (مفعلة وغير مفعلة)
                          ->orWhere('status', 1); // أو الأسئلة المفعلة من الآخرين
                })
                ->get();
        }

        // إضافة معلومات الحد اليومي
        $todayUsage = AiUsageLog::getTodayUsage($teacherId, 'add_questions');
        $remaining = $permission->questions_limit > 0 ? $permission->questions_limit - $todayUsage : -1;

        // إضافة معلومات إضافية للأسئلة
        $questionsWithMeta = $questions->map(function($question) use ($teacherId, $user) {
            $questionArray = $question->toArray();
            $questionArray['can_request_activation'] = (
                $user->type === 2 && // معلم
                $question->user_id === $teacherId && // السؤال ملكه
                $question->status == 0 // السؤال غير مفعل
            );
            $questionArray['is_own_question'] = ($question->user_id === $teacherId);
            return $questionArray;
        });

        return response()->json([
            'questions' => $questionsWithMeta,
            'usage_info' => [
                'today_usage' => $todayUsage,
                'limit' => $permission->questions_limit,
                'remaining' => $remaining
            ]
        ]);
    }

    public function addQuestion(Request $request)
    {
        $request->validate([
            'content_id' => 'required|exists:contents,id',
            'question' => 'required|string',
            'answer' => 'required|string',
            'difficulty' => 'in:سهل,متوسط,صعب',
        ]);

        $teacherId = auth()->id();

        // فحص الصلاحيات
        $permission = AiPermission::where('teacher_id', $teacherId)->first();
        if (!$permission || !$permission->can_add_questions) {
            return response()->json(['error' => 'لا تملك صلاحية إضافة الأسئلة'], 403);
        }

        // فحص الحد اليومي
        if ($permission->questions_limit > 0) {
            if (!AiUsageLog::canPerformAction($teacherId, 'add_questions', $permission->questions_limit)) {
                $todayUsage = AiUsageLog::getTodayUsage($teacherId, 'add_questions');
                return response()->json([
                    'error' => "تم استنفاد الحد اليومي ({$todayUsage}/{$permission->questions_limit})"
                ], 429);
            }
        }

        $question = QuestionAnswer::create([
            'user_id' => $teacherId,
            'content_id' => $request->content_id,
            'question' => $request->question,
            'answer' => $request->answer,
            'difficulty' => $request->difficulty ?? 'متوسط',
            'type' => 'يدوي',
            'status' => auth()->user()->type === 2 ? 0 : 1, // المعلمون: غير مفعل، الأدمن: مفعل
            'is_ai_generated' => false,
        ]);

        // تسجيل الاستخدام
        AiUsageLog::logUsage($teacherId, 'add_questions');

        return response()->json(['success' => true, 'question' => $question]);
    }

    public function generateSuggestions(Request $request)
    {
        $contentId = $request->input('content_id');
        $content = Content::find($contentId);

        if (!$content) {
            return response()->json(['error' => 'المحتوى غير موجود'], 404);
        }

        $description = json_decode($content->description, true);
        $textContent = '';

        // التركيز على محتوى المعلم فقط
        if ($description && !empty($description['teacher'])) {
            $textContent = strip_tags($description['teacher']);
        }

        if (empty(trim($textContent))) {
            return response()->json(['error' => 'لا يوجد محتوى للمعلم لإنشاء اقتراحات'], 400);
        }

        $prompt = "اقترح 3 أسئلة تعليمية مع إجاباتها بناءً على محتوى المعلم التالي:\n\n".trim($textContent);

        $suggestions = $this->callGeminiAPI($prompt, 500);

        return response()->json(['suggestions' => $suggestions]);
    }

    private function analyzeWithGemini($content, $media)
    {
        // التركيز على محتوى المعلم فقط
        $mediaType = 'teacher_media';
        $specificMedia = $content->getMedia($mediaType);

        // جمع جميع المحتويات للتحليل الشامل
        $allContent = $this->gatherAllTeacherContent($content, $specificMedia);

        // إنشاء تحليل شامل واحد
        $comprehensiveAnalysis = $this->createUnifiedAnalysis($allContent);

        // تحويل التحليل إلى HTML منسق
        $htmlAnalysis = $this->convertAnalysisToHTML($comprehensiveAnalysis);

        // حفظ التحليل في قاعدة البيانات
        $aiAnalysis = AiAnalysis::updateOrCreate(
            ['content_id' => $content->id],
            [
                'text_analysis' => null,
                'image_analyses' => null,
                'video_analyses' => null,
                'comprehensive_analysis' => $htmlAnalysis,
                'overall_rating' => $this->calculateUnifiedRating($comprehensiveAnalysis),
            ]
        );

        // استخراج الأسئلة من التحليل الشامل (بدون حفظ تلقائي)
        $questions = $this->extractQuestionsFromUnifiedAnalysis($comprehensiveAnalysis);

        return $this->formatUnifiedResult($htmlAnalysis, $allContent['stats'], $questions);
    }

    private function gatherAllTeacherContent($content, $specificMedia)
    {
        \Log::info('Starting to gather teacher content for content ID: '.$content->id);

        $allContent = [
            'text' => '',
            'images' => [],
            'videos' => [],
            'stats' => ['images' => 0, 'videos' => 0, 'text_length' => 0],
        ];

        // جمع النص من محتوى المعلم
        $description = json_decode($content->description, true);
        if ($description && isset($description['teacher'])) {
            $allContent['text'] = strip_tags($description['teacher']);
            $allContent['stats']['text_length'] = strlen($allContent['text']);
            \Log::info('Teacher text content length: '.$allContent['stats']['text_length']);
        } else {
            \Log::warning('No teacher content found in description');
        }

        // جمع الصور والفيديوهات من MediaLibrary
        foreach ($specificMedia as $item) {
            if (strpos($item->mime_type, 'image/') === 0) {
                try {
                    $imagePath = $item->getPath();
                    if (file_exists($imagePath)) {
                        $imageData = base64_encode(file_get_contents($imagePath));
                        $allContent['images'][] = [
                            'data' => $imageData,
                            'mime_type' => $item->mime_type,
                            'name' => $item->name,
                        ];
                        ++$allContent['stats']['images'];
                    }
                } catch (\Exception $e) {
                    \Log::error('Error loading media image: '.$e->getMessage());
                }
            } elseif (strpos($item->mime_type, 'video/') === 0) {
                try {
                    $audioPath = $this->extractAudioFromVideo($item->getPath());
                    if ($audioPath && file_exists($audioPath)) {
                        $audioData = base64_encode(file_get_contents($audioPath));
                        $allContent['videos'][] = [
                            'data' => $audioData,
                            'name' => $item->name,
                        ];
                        ++$allContent['stats']['videos'];
                        unlink($audioPath); // حذف الملف المؤقت
                    }
                } catch (\Exception $e) {
                    \Log::error('Error processing video: '.$e->getMessage());
                }
            }
        }

        // جمع الصور من محتوى النص
        if ($description && isset($description['teacher'])) {
            preg_match_all('/<img[^>]+src=["\'](.*?)["\']/i', $description['teacher'], $matches);

            if (!empty($matches[1])) {
                foreach ($matches[1] as $imageUrl) {
                    if (strpos($imageUrl, '/storage/images/') !== false) {
                        try {
                            $path = parse_url($imageUrl, PHP_URL_PATH);
                            $localPath = str_replace('/storage/', 'storage/app/public/', $path);
                            $fullPath = base_path($localPath);

                            if (file_exists($fullPath)) {
                                $imageData = base64_encode(file_get_contents($fullPath));
                                $mimeType = mime_content_type($fullPath) ?: 'image/jpeg';
                                $allContent['images'][] = [
                                    'data' => $imageData,
                                    'mime_type' => $mimeType,
                                    'name' => basename($fullPath),
                                ];
                                ++$allContent['stats']['images'];
                            }
                        } catch (\Exception $e) {
                            \Log::warning('Could not load content image: '.$e->getMessage());
                        }
                    }
                }
            }
        }

        \Log::info('Gathered content stats: ', $allContent['stats']);

        return $allContent;
    }

    private function createUnifiedAnalysis($allContent)
    {
        // التحقق من وجود محتوى للتحليل
        if (empty($allContent['text']) && empty($allContent['images']) && empty($allContent['videos'])) {
            \Log::warning('No content found for analysis');

            return 'لا يوجد محتوى للمعلم للتحليل. يرجى إضافة نصوص أو صور أو فيديوهات في قسم المعلم.';
        }

        // إنشاء prompt شامل يجمع كل المحتويات
        $prompt = "أنت خبير تعليمي متخصص. قم بتحليل المحتوى التعليمي التالي للمعلم بشكل شامل ومتكامل:\n\n";

        // إضافة النص إذا كان موجوداً
        if (!empty($allContent['text'])) {
            $prompt .= "المحتوى النصي:\n".$allContent['text']."\n\n";
        }

        // إنشاء payload للـ API
        $parts = [['text' => $prompt]];

        // إضافة الصور
        foreach ($allContent['images'] as $index => $image) {
            $parts[] = ['inline_data' => [
                'mime_type' => $image['mime_type'],
                'data' => $image['data'],
            ]];
        }

        // إضافة الفيديوهات (الصوت المستخرج)
        foreach ($allContent['videos'] as $index => $video) {
            $parts[] = ['inline_data' => [
                'mime_type' => 'audio/mp3',
                'data' => $video['data'],
            ]];
        }

        // إضافة تعليمات التحليل الشامل
        $analysisInstructions = "\n\nقدم تحليلاً شاملاً ومتكاملاً يشمل:\n";
        $analysisInstructions .= "1. تقييم شامل للمحتوى التعليمي\n";
        $analysisInstructions .= "2. مدى وضوح وفعالية المحتوى\n";
        $analysisInstructions .= "3. التناسق بين النصوص والصور والفيديوهات\n";
        $analysisInstructions .= "4. نقاط القوة في المحتوى\n";
        $analysisInstructions .= "5. المجالات التي تحتاج تحسين\n";
        $analysisInstructions .= "6. اقتراحات محددة للتطوير\n";
        $analysisInstructions .= "7. تقييم مدى ملاءمة المحتوى للطلاب\n\n";
        $analysisInstructions .= 'اجعل التحليل مفصلاً ومفيداً للمعلم.';

        $parts[0]['text'] .= $analysisInstructions;

        $payload = [
            'contents' => [['parts' => $parts]],
            'generationConfig' => [
                'temperature' => 0.3,
                'maxOutputTokens' => 1000,
            ],
        ];

        return $this->callGeminiAPIWithPayload($payload, 45);
    }

    private function extractQuestionsFromUnifiedAnalysis($analysis)
    {
        if (empty($analysis)) {
            return [];
        }

        $prompt = "بناءً على التحليل التالي، أنشئ 5 أسئلة تعليمية متنوعة مع إجاباتها بصيغة JSON:\n";
        $prompt .= "[{\"question\": \"السؤال\", \"answer\": \"الإجابة\", \"difficulty\": \"سهل|متوسط|صعب\", \"type\": \"فهم|تطبيق|تحليل\"}]\n\n";
        $prompt .= "التحليل:\n".$analysis;

        $response = $this->callGeminiAPI($prompt, 800);

        if ($response && $response !== 'فشل في الاتصال بخدمة التحليل. يرجى المحاولة لاحقاً.') {
            preg_match('/\[.*\]/s', $response, $matches);
            if (!empty($matches[0])) {
                $questions = json_decode($matches[0], true);
                if (is_array($questions)) {
                    return $questions;
                }
            }
        }

        return [];
    }

    private function calculateUnifiedRating($analysis)
    {
        if (empty($analysis)) {
            return 3;
        }

        // تحليل بسيط للنص لتقدير التقييم
        $positiveWords = ['ممتاز', 'جيد', 'واضح', 'مفيد', 'شامل', 'متميز', 'فعال'];
        $negativeWords = ['ضعيف', 'غير واضح', 'ينقص', 'يحتاج تحسين', 'مشكلة'];

        $positiveCount = 0;
        $negativeCount = 0;

        foreach ($positiveWords as $word) {
            $positiveCount += substr_count($analysis, $word);
        }

        foreach ($negativeWords as $word) {
            $negativeCount += substr_count($analysis, $word);
        }

        if ($positiveCount > $negativeCount * 2) {
            return 5;
        } elseif ($positiveCount > $negativeCount) {
            return 4;
        } elseif ($negativeCount > $positiveCount) {
            return 2;
        }

        return 3;
    }

    private function convertAnalysisToHTML($analysis)
    {
        if (empty($analysis)) {
            return '<p class="text-muted">لا يوجد تحليل متاح</p>';
        }

        // تحويل النص إلى HTML منسق
        $html = nl2br(htmlspecialchars($analysis));

        // تحسين التنسيق
        $html = preg_replace('/\*\*(.*?)\*\*/', '<strong>$1</strong>', $html);
        $html = preg_replace('/\*(.*?)\*/', '<em>$1</em>', $html);
        $html = preg_replace('/\d+\./m', '<span class="text-primary fw-bold">$0</span>', $html);

        return '<div class="analysis-text" style="line-height: 1.8; font-size: 14px;">' . $html . '</div>';
    }

    private function formatUnifiedResult($analysis, $stats, $questions = [])
    {
        $result = '<div class="ai-analysis">';
        $result .= '<div class="card">';
        $result .= '<div class="card-header bg-primary text-white">';
        $result .= '<h4 class="mb-0"><i class="fas fa-brain"></i> التحليل الشامل لمحتوى المعلم</h4>';
        $result .= '</div>';

        $result .= '<div class="card-body">';
        $result .= '<div class="alert alert-info">';
        $result .= '<h5 class="text-primary"><i class="fas fa-chart-line"></i> التحليل الشامل:</h5>';
        $result .= '<div class="analysis-content">' . $analysis . '</div>';
        $result .= '</div>';

        // إضافة الأسئلة المستخرجة إذا كانت موجودة
        if (!empty($questions)) {
            $result .= '<div class="mt-4" id="suggestedQuestionsSection">';
            $result .= '<div class="card border-info">';
            $result .= '<div class="card-header bg-info text-white d-flex justify-content-between align-items-center">';
            $result .= '<h5 class="mb-0"><i class="fas fa-lightbulb"></i> الأسئلة المقترحة من الذكاء الاصطناعي</h5>';
            $result .= '<span class="badge bg-light text-info">'.count($questions).' سؤال</span>';
            $result .= '</div>';
            $result .= '<div class="card-body">';
            $result .= '<ul class="list-group list-group-flush">';
            foreach ($questions as $index => $qa) {
                $result .= '<li class="list-group-item" data-question="'.htmlspecialchars($qa['question']).'" data-answer="'.htmlspecialchars($qa['answer']).'" data-difficulty="'.($qa['difficulty'] ?? 'متوسط').'" data-type="'.($qa['type'] ?? 'عام').'">';
                $result .= '<div class="d-flex justify-content-between align-items-start">';
                $result .= '<div class="flex-grow-1">';
                $result .= '<div class="fw-bold text-primary mb-2">س'.($index + 1).': '.$qa['question'].'</div>';
                $result .= '<div class="text-muted mb-2">ج: '.$qa['answer'].'</div>';
                $result .= '<div>';
                $result .= '<span class="badge bg-'.($qa['difficulty'] == 'صعب' ? 'danger' : ($qa['difficulty'] == 'متوسط' ? 'warning' : 'success')).'">'.($qa['difficulty'] ?? 'متوسط').'</span>';
                $result .= '<span class="badge bg-info ms-1">'.($qa['type'] ?? 'عام').'</span>';
                $result .= '</div></div>';
                $result .= '<div class="btn-group-vertical" role="group">';
                $result .= '<button class="btn btn-sm btn-outline-success mb-1 save-suggested-question-btn" data-index="'.$index.'" data-question="'.htmlspecialchars($qa['question']).'" data-answer="'.htmlspecialchars($qa['answer']).'" data-difficulty="'.($qa['difficulty'] ?? 'متوسط').'" data-type="'.($qa['type'] ?? 'عام').'"><i class="fas fa-save"></i> حفظ</button>';
                $result .= '<button class="btn btn-sm btn-outline-primary mb-1 edit-suggested-question-btn" data-index="'.$index.'" data-question="'.htmlspecialchars($qa['question']).'" data-answer="'.htmlspecialchars($qa['answer']).'" data-difficulty="'.($qa['difficulty'] ?? 'متوسط').'" data-type="'.($qa['type'] ?? 'عام').'"><i class="fas fa-edit"></i> تعديل</button>';
                $result .= '<button class="btn btn-sm btn-outline-danger delete-suggested-question-btn" data-index="'.$index.'"><i class="fas fa-trash"></i> حذف</button>';
                $result .= '</div></div></li>';
            }
            $result .= '</ul></div>';
        }

        $result .= '</div>';

        // إحصائيات
        $result .= '<div class="card-footer">';
        $result .= '<div class="row text-center">';

        if ($stats['text_length'] > 0) {
            $result .= '<div class="col-md-3">';
            $result .= '<div class="stat-card bg-info text-white p-3 rounded">';
            $result .= '<i class="fas fa-file-text fa-2x mb-2"></i>';
            $result .= '<h5>'.$stats['text_length'].'</h5>';
            $result .= '<small>حرف في النص</small>';
            $result .= '</div></div>';
        }

        if ($stats['images'] > 0) {
            $result .= '<div class="col-md-3">';
            $result .= '<div class="stat-card bg-success text-white p-3 rounded">';
            $result .= '<i class="fas fa-images fa-2x mb-2"></i>';
            $result .= '<h5>'.$stats['images'].'</h5>';
            $result .= '<small>الصور المحللة</small>';
            $result .= '</div></div>';
        }

        if ($stats['videos'] > 0) {
            $result .= '<div class="col-md-3">';
            $result .= '<div class="stat-card bg-warning text-white p-3 rounded">';
            $result .= '<i class="fas fa-video fa-2x mb-2"></i>';
            $result .= '<h5>'.$stats['videos'].'</h5>';
            $result .= '<small>الفيديوهات المحللة</small>';
            $result .= '</div></div>';
        }

        $result .= '<div class="col-md-3">';
        $result .= '<div class="stat-card bg-primary text-white p-3 rounded">';
        $result .= '<i class="fas fa-check-circle fa-2x mb-2"></i>';
        $result .= '<h5>مكتمل</h5>';
        $result .= '<small>حالة التحليل</small>';
        $result .= '</div></div>';

        $result .= '</div></div></div>';

        // إضافة JavaScript لحفظ الأسئلة
        $result .= '<script>
            function saveQuestion(btn) {
                const li = btn.closest("li");
                const question = li.getAttribute("data-question");
                const answer = li.getAttribute("data-answer");

                // تغيير حالة الزر فوراً
                btn.innerHTML = "<i class=\"fas fa-spinner fa-spin\"></i> جاري الحفظ...";
                btn.disabled = true;

                // محاكاة عملية الحفظ (سيتم استبدالها لاحقاً)
                setTimeout(() => {
                    btn.innerHTML = "<i class=\"fas fa-check\"></i> تم الحفظ";
                    btn.classList.remove("btn-outline-primary");
                    btn.classList.add("btn-success");

                    // إظهار رسالة نجاح
                    const toast = document.createElement("div");
                    toast.className = "alert alert-success alert-dismissible fade show position-fixed";
                    toast.style.cssText = "top: 20px; right: 20px; z-index: 9999; min-width: 300px;";
                    toast.innerHTML = `
                        <strong>تم الحفظ!</strong> تم إضافة السؤال والإجابة بنجاح.
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    `;
                    document.body.appendChild(toast);

                    setTimeout(() => {
                        if (toast.parentNode) {
                            toast.parentNode.removeChild(toast);
                        }
                    }, 3000);
                }, 1000);
            }
        </script>';

        $result .= '<style>
            .stat-card {
                transition: transform 0.2s;
            }
            .stat-card:hover {
                transform: translateY(-2px);
            }
            .analysis-content {
                line-height: 1.8;
                font-size: 14px;
            }
            .save-question-btn {
                transition: all 0.3s ease;
            }
            .save-question-btn:hover {
                transform: scale(1.05);
            }
        </style></div>';

        return $result;
    }

    private function createComprehensiveAnalysis($textAnalysis, $imageAnalyses, $audioAnalyses)
    {
        if (empty($imageAnalyses) && empty($audioAnalyses)) {
            return null;
        }

        $combinedContent = 'النص: '.$textAnalysis."\n";

        if (!empty($imageAnalyses)) {
            $combinedContent .= 'الصور: '.implode(' ', $imageAnalyses)."\n";
        }

        if (!empty($audioAnalyses)) {
            $combinedContent .= 'الفيديوهات: '.implode(' ', $audioAnalyses)."\n";
        }

        $prompt = "تقييم شامل للمحتوى التعليمي:\n{$combinedContent}\n\nقدم تقييماً مختصراً يشمل نقاط القوة والتحسين المطلوب.";

        return $this->callGeminiAPI($prompt, 500);
    }

    private function analyzeText($content, $mediaType)
    {
        $description = json_decode($content->description, true);
        $contentType = str_replace('_media', '', $mediaType);
        $textContent = $description[$contentType] ?? '';

        $cleanText = strip_tags($textContent);

        if (empty(trim($cleanText))) {
            return 'لا يوجد محتوى نصي للتحليل';
        }

        $prompt = "أنت خبير تعليمي. قم بتحليل النص التالي:\n\n{$cleanText}\n\nقدم تحليلاً يشمل:\n1. الوضوح والفهم\n2. التنظيم والتسلسل\n3. القيمة التعليمية\n4. نقاط القوة\n5. اقتراحات للتحسين";

        return $this->callGeminiAPI($prompt, 600);
    }

    private function analyzeImages($specificMedia, $content, $mediaType)
    {
        $analyses = [];
        $imageNumber = 1;

        // تحليل صور MediaLibrary
        foreach ($specificMedia as $item) {
            if (strpos($item->mime_type, 'image/') === 0) {
                try {
                    $imagePath = $item->getPath();
                    if (file_exists($imagePath)) {
                        $imageData = base64_encode(file_get_contents($imagePath));
                        $analysis = $this->analyzeSingleImage($imageData, $item->mime_type, $imageNumber);
                        if ($analysis) {
                            $analyses[] = $analysis;
                            ++$imageNumber;
                        }
                    }
                } catch (\Exception $e) {
                    \Log::error('Error loading media image: '.$e->getMessage());
                }
            }
        }

        // تحليل صور المحتوى - إصلاح لقراءة جميع الصور
        $description = json_decode($content->description, true);
        $contentType = str_replace('_media', '', $mediaType);

        if ($description && isset($description[$contentType])) {
            preg_match_all('/<img[^>]+src=["\'](.*?)["\']/i', $description[$contentType], $matches);

            if (!empty($matches[1])) {
                \Log::info('Found '.count($matches[1]).' images in content description');
                foreach ($matches[1] as $index => $imageUrl) {
                    \Log::info('Processing content image '.($index + 1).': '.$imageUrl);
                    if (strpos($imageUrl, '/storage/images/') !== false) {
                        try {
                            $path = parse_url($imageUrl, PHP_URL_PATH);
                            $localPath = str_replace('/storage/', 'storage/app/public/', $path);
                            $fullPath = base_path($localPath);

                            if (file_exists($fullPath)) {
                                $imageData = base64_encode(file_get_contents($fullPath));
                                $mimeType = mime_content_type($fullPath) ?: 'image/jpeg';
                                $analysis = $this->analyzeSingleImage($imageData, $mimeType, $imageNumber);
                                if ($analysis) {
                                    $analyses[] = $analysis;
                                    ++$imageNumber;
                                    \Log::info('Successfully analyzed content image '.($imageNumber - 1));
                                }
                            } else {
                                \Log::warning('Content image file not found: '.$fullPath);
                            }
                        } catch (\Exception $e) {
                            \Log::warning('Could not load content image: '.$e->getMessage());
                        }
                    }
                }
            }
        }

        return $analyses;
    }

    private function analyzeSingleImage($imageData, $mimeType, $imageNumber)
    {
        $prompt = "أنت خبير في تحليل المحتوى التعليمي. قم بتحليل الصورة رقم {$imageNumber}:\n\nوصف المحتوى التعليمي والعناصر البصرية وتقييم جودة التصميم والوضوح.";

        $payload = [
            'contents' => [[
                'parts' => [
                    ['text' => $prompt],
                    ['inline_data' => ['mime_type' => $mimeType, 'data' => $imageData]],
                ],
            ]],
            'generationConfig' => ['temperature' => 0.3, 'maxOutputTokens' => 400],
        ];

        $result = $this->callGeminiAPIWithPayload($payload);

        return $result ? "📸 **الصورة رقم {$imageNumber}:**\n".$result : null;
    }

    private function analyzeVideos($specificMedia)
    {
        $analyses = [];
        $videoNumber = 1;

        foreach ($specificMedia as $item) {
            if (strpos($item->mime_type, 'video/') === 0) {
                try {
                    \Log::info('Processing video: '.$item->name.' ('.$item->mime_type.')');
                    $audioPath = $this->extractAudioFromVideo($item->getPath());

                    if ($audioPath && file_exists($audioPath)) {
                        \Log::info('Audio extracted successfully: '.$audioPath);
                        $analysis = $this->analyzeSingleAudio($audioPath, $videoNumber);
                        if ($analysis) {
                            $analyses[] = $analysis;
                        }
                        unlink($audioPath); // حذف الملف المؤقت
                        ++$videoNumber;
                    } else {
                        \Log::error('Failed to extract audio from video: '.$item->name);
                    }
                } catch (\Exception $e) {
                    \Log::error('Error processing video '.$item->name.': '.$e->getMessage());
                }
            }
        }

        return $analyses;
    }

    private function analyzeSingleAudio($audioPath, $videoNumber)
    {
        try {
            $audioData = base64_encode(file_get_contents($audioPath));
            $prompt = "أنت خبير في تحليل المحتوى الصوتي التعليمي. قم بتحليل الصوت المستخرج من الفيديو رقم {$videoNumber}:\n\nاستخرج المحتوى التعليمي الرئيسي، والنقاط المهمة، وأي أسئلة مطروحة مع إجاباتها.";

            $payload = [
                'contents' => [[
                    'parts' => [
                        ['text' => $prompt],
                        ['inline_data' => ['mime_type' => 'audio/mp3', 'data' => $audioData]],
                    ],
                ]],
                'generationConfig' => ['temperature' => 0.3, 'maxOutputTokens' => 500],
            ];

            $result = $this->callGeminiAPIWithPayload($payload, 35);

            return $result ? "🎥 **الفيديو رقم {$videoNumber}:**\n".$result : null;
        } catch (\Exception $e) {
            \Log::error('Audio analysis error: '.$e->getMessage());

            return null;
        }
    }

    private function combineAnalyses($textAnalysis, $imageAnalyses, $audioAnalyses, $comprehensiveAnalysis = null)
    {
        $result = '<div class="analysis-content">';

        // تحليل النص
        if (!empty($textAnalysis)) {
            $result .= '<div class="mb-4"><h5 class="text-primary"><i class="fas fa-file-text"></i> تحليل المحتوى النصي:</h5>';
            $result .= '<div class="alert alert-info">'.$textAnalysis.'</div></div>';
        }

        // تحليل الصور
        if (!empty($imageAnalyses)) {
            $result .= '<div class="mb-4"><h5 class="text-success"><i class="fas fa-images"></i> تحليل الصور:</h5>';
            foreach ($imageAnalyses as $analysis) {
                $result .= '<div class="alert alert-light border-start border-success border-3 mb-2">'.$analysis.'</div>';
            }
            $result .= '</div>';
        }

        // تحليل الفيديوهات مع استخراج الأسئلة
        if (!empty($audioAnalyses)) {
            $result .= '<div class="mb-4"><h5 class="text-warning"><i class="fas fa-video"></i> تحليل الفيديوهات:</h5>';
            foreach ($audioAnalyses as $analysis) {
                $result .= '<div class="alert alert-warning">'.$analysis.'</div>';
            }

            // استخراج الأسئلة والأجوبة من تحليل الفيديوهات
            $questions = $this->extractQuestionsFromAnalysis($audioAnalyses);
            if (!empty($questions)) {
                $result .= '<div class="mt-4"><h6 class="text-info"><i class="fas fa-question-circle"></i> الأسئلة المستخرجة:</h6>';
                $result .= '<ul class="list-group">';
                foreach ($questions as $index => $qa) {
                    $result .= '<li class="list-group-item d-flex justify-content-between align-items-start" data-question="'.htmlspecialchars($qa['question']).'" data-answer="'.htmlspecialchars($qa['answer']).'">';
                    $result .= '<div class="ms-2 me-auto">';
                    $result .= '<div class="fw-bold text-primary">س'.($index + 1).': '.$qa['question'].'</div>';
                    $result .= '<small class="text-muted">ج: '.$qa['answer'].'</small>';
                    $result .= '</div>';
                    $result .= '<button class="btn btn-sm btn-outline-primary save-question-btn" onclick="saveQuestion(this)"><i class="fas fa-plus"></i> إضافة</button>';
                    $result .= '</li>';
                }
                $result .= '</ul></div>';
            }
            $result .= '</div>';
        }

        $result .= '</div>';

        return $result;
    }

    private function extractQuestionsFromAnalysis($audioAnalyses)
    {
        if (empty($audioAnalyses)) {
            return [];
        }

        $combinedAnalysis = implode('\n\n', $audioAnalyses);
        $prompt = "من التحليل التالي، أنشئ 3 أسئلة بصيغة JSON:\n[{\"question\": \"السؤال\", \"answer\": \"الإجابة\", \"difficulty\": \"متوسط\", \"type\": \"فهم\"}]\n\nالتحليل:\n".$combinedAnalysis;

        $response = $this->callGeminiAPI($prompt, 800);

        if ($response && $response !== 'فشل في الاتصال بخدمة التحليل. يرجى المحاولة لاحقاً.') {
            preg_match('/\[.*\]/s', $response, $matches);
            if (!empty($matches[0])) {
                $questions = json_decode($matches[0], true);
                if (is_array($questions)) {
                    return $questions;
                }
            }
        }

        return [];
    }

    private function callGeminiAPI($prompt, $maxTokens = 400, $retries = 3)
    {
        $payload = [
            'contents' => [['parts' => [['text' => $prompt]]]],
            'generationConfig' => ['temperature' => 0.3, 'maxOutputTokens' => $maxTokens],
        ];

        return $this->callGeminiAPIWithPayload($payload, 25, $retries);
    }

    private function callGeminiAPIWithPayload($payload, $timeout = 25, $retries = 2)
    {
        $apiKey = env('GEMINI_API_KEY');
        $url = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key='.$apiKey;

        for ($attempt = 1; $attempt <= $retries; ++$attempt) {
            try {
                $response = Http::timeout($timeout)->post($url, $payload);

                if ($response->successful()) {
                    $responseData = $response->json();

                    return $responseData['candidates'][0]['content']['parts'][0]['text'] ?? null;
                }

                $error = $response->json();

                // معالجة خاصة لخطأ استنفاد الحد اليومي
                if (isset($error['error']['code']) && $error['error']['code'] == 429) {
                    \Log::error('Gemini API quota exceeded: '.json_encode($error));

                    return 'تم استنفاد الحد اليومي لخدمة التحليل. يرجى المحاولة غداً.';
                }

                // إعادة المحاولة في حالة الخدمة محملة
                if (isset($error['error']['code']) && $error['error']['code'] == 503 && $attempt < $retries) {
                    \Log::info("Gemini API overloaded, retrying attempt {$attempt}/{$retries}");
                    sleep(2);
                    continue;
                }

                \Log::warning('Gemini API failed: '.json_encode($error));

                return 'فشل في الاتصال بخدمة التحليل.';
            } catch (\Exception $e) {
                \Log::error("Gemini API exception attempt {$attempt}: ".$e->getMessage());
                if ($attempt < $retries) {
                    sleep(1);
                    continue;
                }

                return 'خطأ في الاتصال بخدمة التحليل.';
            }
        }

        return 'فشل في الاتصال بخدمة التحليل.';
    }

    private function saveQuestions($contentId, $questions)
    {
        if (empty($questions)) {
            \Log::info('No questions to save for content ID: '.$contentId);

            return;
        }

        \Log::info('Saving '.count($questions).' questions for content ID: '.$contentId);

        foreach ($questions as $index => $qa) {
            try {
                if (!isset($qa['question']) || !isset($qa['answer'])) {
                    \Log::warning('Skipping invalid question at index '.$index.': missing question or answer');
                    continue;
                }

                QuestionAnswer::create([
                    'content_id' => $contentId,
                    'question' => $qa['question'],
                    'answer' => $qa['answer'],
                    'difficulty' => $qa['difficulty'] ?? 'متوسط',
                    'type' => $qa['type'] ?? 'عام',
                    'is_ai_generated' => true,
                ]);

                \Log::info('Successfully saved question '.($index + 1));
            } catch (\Exception $e) {
                \Log::error('Failed to save question '.($index + 1).': '.$e->getMessage());
            }
        }
    }

    private function calculateOverallRating($textAnalysis, $imageAnalyses, $audioAnalyses)
    {
        $ratings = [];

        if (!empty($textAnalysis)) {
            $ratings[] = 4;
        }
        if (!empty($imageAnalyses)) {
            $ratings[] = 4;
        }
        if (!empty($audioAnalyses)) {
            $ratings[] = 4;
        }

        return !empty($ratings) ? round(array_sum($ratings) / count($ratings)) : 3;
    }

    private function formatResult($analysis, $imageCount, $audioCount = 0)
    {
        $result = '<div class="ai-analysis">';
        $result .= '<div class="card">';
        $result .= '<div class="card-header bg-primary text-white">';
        $result .= '<h4 class="mb-0"><i class="fas fa-brain"></i> تحليل المحتوى بواسطة الذكاء الاصطناعي</h4>';
        $result .= '</div>';
        $result .= '<div class="card-body">'.$analysis.'</div>';

        // إحصائيات احترافية
        $result .= '<div class="card-footer">';
        $result .= '<div class="row text-center">';
        $result .= '<div class="col-md-4">';
        $result .= '<div class="stat-card bg-info text-white p-3 rounded">';
        $result .= '<i class="fas fa-images fa-2x mb-2"></i>';
        $result .= '<h5>'.$imageCount.'</h5>';
        $result .= '<small>الصور المحللة</small>';
        $result .= '</div></div>';

        if ($audioCount > 0) {
            $result .= '<div class="col-md-4">';
            $result .= '<div class="stat-card bg-warning text-white p-3 rounded">';
            $result .= '<i class="fas fa-video fa-2x mb-2"></i>';
            $result .= '<h5>'.$audioCount.'</h5>';
            $result .= '<small>الفيديوهات المحللة</small>';
            $result .= '</div></div>';
        }

        $result .= '<div class="col-md-4">';
        $result .= '<div class="stat-card bg-success text-white p-3 rounded">';
        $result .= '<i class="fas fa-check-circle fa-2x mb-2"></i>';
        $result .= '<h5>مكتمل</h5>';
        $result .= '<small>حالة التحليل</small>';
        $result .= '</div></div>';
        $result .= '</div></div></div>';

        $result .= '<script>
            function saveQuestion(btn) {
                const li = btn.closest("li");
                const question = li.getAttribute("data-question");
                const answer = li.getAttribute("data-answer");

                // تغيير حالة الزر فوراً
                btn.innerHTML = "<i class=\"fas fa-spinner fa-spin\"></i> جاري الحفظ...";
                btn.disabled = true;

                // محاكاة عملية الحفظ (سيتم استبدالها لاحقاً)
                setTimeout(() => {
                    btn.innerHTML = "<i class=\"fas fa-check\"></i> تم الحفظ";
                    btn.classList.remove("btn-outline-primary");
                    btn.classList.add("btn-success");

                    // إظهار رسالة نجاح
                    const toast = document.createElement("div");
                    toast.className = "alert alert-success alert-dismissible fade show position-fixed";
                    toast.style.cssText = "top: 20px; right: 20px; z-index: 9999; min-width: 300px;";
                    toast.innerHTML = `
                        <strong>تم الحفظ!</strong> تم إضافة السؤال والإجابة بنجاح.
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    `;
                    document.body.appendChild(toast);

                    setTimeout(() => {
                        if (toast.parentNode) {
                            toast.parentNode.removeChild(toast);
                        }
                    }, 3000);
                }, 1000);
            }
        </script>';

        $result .= '<style>
            .stat-card {
                transition: transform 0.2s;
            }
            .stat-card:hover {
                transform: translateY(-2px);
            }
            .save-question-btn {
                transition: all 0.3s ease;
            }
            .save-question-btn:hover {
                transform: scale(1.05);
            }
        </style></div>';

        return $result;
    }

    public function saveQuestion(Request $request)
    {
        $request->validate([
            'question' => 'required|string',
            'answer' => 'required|string',
            'content_id' => 'required|numeric',
        ]);

        $teacherId = auth()->id();

        // فحص الصلاحيات
        $permission = AiPermission::where('teacher_id', $teacherId)->first();
        if (!$permission || !$permission->can_add_questions) {
            return response()->json(['error' => 'لا تملك صلاحية إضافة الأسئلة'], 403);
        }

        // فحص الحد اليومي
        if ($permission->questions_limit > 0) {
            if (!AiUsageLog::canPerformAction($teacherId, 'add_questions', $permission->questions_limit)) {
                $todayUsage = AiUsageLog::getTodayUsage($teacherId, 'add_questions');
                return response()->json([
                    'error' => "تم استنفاد الحد اليومي ({$todayUsage}/{$permission->questions_limit})"
                ], 429);
            }
        }

        $question = QuestionAnswer::create([
            'user_id' => $teacherId,
            'content_id' => $request->content_id,
            'question' => $request->question,
            'answer' => $request->answer,
            'difficulty' => 'متوسط',
            'type' => 'مستخرج من الذكاء الاصطناعي',
            'status' => auth()->user()->type === 2 ? 0 : 1, // المعلمون: غير مفعل، الأدمن: مفعل
            'is_ai_generated' => true,
        ]);

        // تسجيل الاستخدام
        AiUsageLog::logUsage($teacherId, 'add_questions');

        \Log::info('Question saved:', $question->toArray());

        return response()->json(['success' => true, 'message' => 'تم حفظ السؤال بنجاح', 'question' => $question]);
    }

    private function extractAudioFromVideo($videoPath)
    {
        try {
            \Log::info('Starting audio extraction from: '.$videoPath);

            // التحقق من وجود الملف
            if (!file_exists($videoPath)) {
                \Log::error('Video file does not exist: '.$videoPath);

                return null;
            }

            $outputPath = storage_path('app/temp/audio_'.uniqid().'.mp3');

            // التأكد من وجود مجلد temp
            $tempDir = dirname($outputPath);
            if (!is_dir($tempDir)) {
                mkdir($tempDir, 0755, true);
                \Log::info('Created temp directory: '.$tempDir);
            }

            // التحقق من وجود FFmpeg
            $ffmpegCheck = shell_exec('which ffmpeg 2>/dev/null');
            if (empty(trim($ffmpegCheck))) {
                \Log::error('FFmpeg is not installed or not in PATH');

                return null;
            }

            // استخدام FFmpeg لاستخراج الصوت
            $command = "ffmpeg -i \"$videoPath\" -vn -acodec mp3 -ab 64k -ar 16000 -ac 1 -y \"$outputPath\" 2>&1";
            \Log::info('Executing FFmpeg command: '.$command);

            $output = shell_exec($command);
            \Log::info('FFmpeg output: '.$output);

            if (file_exists($outputPath) && filesize($outputPath) > 0) {
                \Log::info('Audio extraction successful. File size: '.filesize($outputPath).' bytes');

                return $outputPath;
            } else {
                \Log::error('Audio extraction failed. Output file not created or empty.');

                return null;
            }
        } catch (\Exception $e) {
            \Log::error('Audio extraction error: '.$e->getMessage());

            return null;
        }
    }

    public function suggestQuestions(Request $request)
    {
        try {
            $request->validate([
                'content_id' => 'required|numeric',
            ]);

            $contentId = $request->input('content_id');
            $content = Content::find($contentId);

            if (!$content) {
                return response()->json(['error' => 'المحتوى غير موجود'], 404);
            }

            $description = json_decode($content->description, true);
            $textContent = '';

            // التركيز على محتوى المعلم فقط
            if ($description && !empty($description['teacher'])) {
                $textContent = strip_tags($description['teacher']);
            }

            if (empty(trim($textContent))) {
                return response()->json(['error' => 'لا يوجد محتوى للمعلم لإنشاء اقتراحات'], 400);
            }

            $prompt = "اقترح 3 أسئلة تعليمية مع إجاباتها بناءً على محتوى المعلم التالي:\n\n".trim($textContent);
            $prompt .= "\n\nقدم الأسئلة بصيغة JSON كالتالي:\n";
            $prompt .= '[{"question": "السؤال", "answer": "الإجابة", "difficulty": "سهل|متوسط|صعب", "type": "فهم|تطبيق|تحليل"}]';

            $suggestions = $this->callGeminiAPI($prompt, 800);

            if ($suggestions && $suggestions !== 'فشل في الاتصال بخدمة التحليل. يرجى المحاولة لاحقاً.') {
                // محاولة استخراج JSON من الاستجابة
                preg_match('/\[.*\]/s', $suggestions, $matches);
                if (!empty($matches[0])) {
                    $questions = json_decode($matches[0], true);
                    if (is_array($questions)) {
                        return response()->json(['success' => true, 'suggestions' => $questions]);
                    }
                }

                // إذا لم يتم العثور على JSON، إرجاع النص كما هو
                return response()->json(['success' => true, 'suggestions' => $suggestions]);
            }

            return response()->json(['error' => 'فشل في إنشاء الاقتراحات'], 500);
        } catch (\Exception $e) {
            \Log::error('Suggest Questions Error: '.$e->getMessage());

            return response()->json(['error' => 'حدث خطأ أثناء إنشاء الاقتراحات'], 500);
        }
    }

    public function generateAIAnswer(Request $request)
    {
        try {
            $request->validate([
                'question' => 'required|string',
                'content_id' => 'required|numeric',
            ]);

            $question = $request->input('question');
            $contentId = $request->input('content_id');
            $content = Content::find($contentId);

            if (!$content) {
                return response()->json(['error' => 'المحتوى غير موجود'], 404);
            }

            // الحصول على محتوى المعلم للسياق
            $description = json_decode($content->description, true);
            $textContent = '';

            if ($description && !empty($description['teacher'])) {
                $textContent = strip_tags($description['teacher']);
            }

            // إنشاء prompt لتوليد الإجابة
            $prompt = "أنت مساعد تعليمي ذكي. بناءً على المحتوى التعليمي التالي، قدم إجابة شاملة ومفيدة للسؤال:\n\n";

            if (!empty($textContent)) {
                $prompt .= "المحتوى التعليمي:\n".trim($textContent)."\n\n";
            }

            $prompt .= 'السؤال: '.$question."\n\n";
            $prompt .= 'قدم إجابة واضحة ومفصلة ومناسبة للمستوى التعليمي.';

            $answer = $this->callGeminiAPI($prompt, 600);

            if ($answer && $answer !== 'فشل في الاتصال بخدمة التحليل. يرجى المحاولة لاحقاً.') {
                return response()->json(['success' => true, 'answer' => $answer]);
            }

            return response()->json(['error' => 'فشل في توليد الإجابة'], 500);
        } catch (\Exception $e) {
            \Log::error('Generate AI Answer Error: '.$e->getMessage());

            return response()->json(['error' => 'حدث خطأ أثناء توليد الإجابة'], 500);
        }
    }

    public function updateQuestion(Request $request)
    {
        try {
            $request->validate([
                'question_id' => 'required|numeric',
                'question' => 'required|string',
                'answer' => 'required|string',
                'difficulty' => 'in:سهل,متوسط,صعب',
                'type' => 'nullable|string',
            ]);

            $question = QuestionAnswer::find($request->question_id);

            if (!$question) {
                return response()->json(['success' => false, 'message' => 'السؤال غير موجود'], 404);
            }

            $question->update([
                'question' => $request->question,
                'answer' => $request->answer,
                'difficulty' => $request->difficulty ?? 'متوسط',
                'type' => $request->type ?? 'عام',
            ]);

            return response()->json(['success' => true, 'message' => 'تم تحديث السؤال بنجاح', 'question' => $question]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'حدث خطأ أثناء تحديث السؤال'], 500);
        }
    }

    /**
     * طلب تفعيل سؤال من قبل المعلم
     */
    public function requestActivation(Request $request)
    {
        $request->validate([
            'question_id' => 'required|exists:questions_answers,id'
        ]);

        $teacherId = auth()->id();
        $question = QuestionAnswer::findOrFail($request->question_id);

        // التأكد من أن السؤال ملك للمعلم
        if ($question->user_id !== $teacherId) {
            return response()->json(['error' => 'لا يمكنك طلب تفعيل هذا السؤال'], 403);
        }

        // التأكد من أن السؤال غير مفعل
        if ($question->status == 1) {
            return response()->json(['error' => 'السؤال مفعل بالفعل'], 400);
        }

        // هنا يمكن إضافة منطق إرسال إشعار للأدمن
        // أو تحديث حقل خاص بطلبات التفعيل
        // لكن حالياً سنكتفي برسالة نجاح

        return response()->json([
            'success' => true,
            'message' => 'تم إرسال طلب التفعيل للإدارة. سيتم مراجعة السؤال وتفعيله قريباً.'
        ]);
    }

    public function deleteQuestion(Request $request)
    {
        try {
            $request->validate([
                'question_id' => 'required|numeric',
            ]);

            $question = QuestionAnswer::find($request->question_id);

            if (!$question) {
                return response()->json(['success' => false, 'message' => 'السؤال غير موجود'], 404);
            }

            $question->delete();

            return response()->json(['success' => true, 'message' => 'تم حذف السؤال بنجاح']);
        } catch (\Exception $e) {
            \Log::error('Delete Question Error: '.$e->getMessage());
            return response()->json(['success' => false, 'message' => 'حدث خطأ أثناء حذف السؤال'], 500);
        }
    }
}
