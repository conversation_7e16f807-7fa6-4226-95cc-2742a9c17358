<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\QuestionAnswer;
use App\Models\User;
use App\Models\Content;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class QuestionManagementController extends Controller
{
    /**
     * عرض جميع الأسئلة للأدمن
     */
    public function index(Request $request)
    {
        $query = QuestionAnswer::with(['user', 'content']);

        // فلترة حسب الحالة
        if ($request->has('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }

        // فلترة حسب المعلم
        if ($request->has('teacher_id') && $request->teacher_id !== '') {
            $query->where('user_id', $request->teacher_id);
        }

        // فلترة حسب النوع
        if ($request->has('type') && $request->type !== '') {
            if ($request->type === 'ai') {
                $query->where('is_ai_generated', 1);
            } elseif ($request->type === 'manual') {
                $query->where('is_ai_generated', 0);
            }
        }

        $questions = $query->orderBy('created_at', 'desc')->paginate(20);
        $teachers = User::where('type', 2)->get();

        return view('admin.questions.index', compact('questions', 'teachers'));
    }

    /**
     * تفعيل سؤال
     */
    public function activate(Request $request)
    {
        $question = QuestionAnswer::findOrFail($request->question_id);
        $question->update(['status' => 1]);

        return response()->json([
            'success' => true,
            'message' => 'تم تفعيل السؤال بنجاح'
        ]);
    }

    /**
     * إلغاء تفعيل سؤال
     */
    public function deactivate(Request $request)
    {
        $question = QuestionAnswer::findOrFail($request->question_id);
        $question->update(['status' => 0]);

        return response()->json([
            'success' => true,
            'message' => 'تم إلغاء تفعيل السؤال بنجاح'
        ]);
    }

    /**
     * تفعيل عدة أسئلة
     */
    public function bulkActivate(Request $request)
    {
        $request->validate([
            'question_ids' => 'required|array',
            'question_ids.*' => 'exists:questions_answers,id'
        ]);

        QuestionAnswer::whereIn('id', $request->question_ids)
            ->update(['status' => 1]);

        return response()->json([
            'success' => true,
            'message' => 'تم تفعيل الأسئلة المحددة بنجاح'
        ]);
    }

    /**
     * إلغاء تفعيل عدة أسئلة
     */
    public function bulkDeactivate(Request $request)
    {
        $request->validate([
            'question_ids' => 'required|array',
            'question_ids.*' => 'exists:questions_answers,id'
        ]);

        QuestionAnswer::whereIn('id', $request->question_ids)
            ->update(['status' => 0]);

        return response()->json([
            'success' => true,
            'message' => 'تم إلغاء تفعيل الأسئلة المحددة بنجاح'
        ]);
    }

    /**
     * حذف سؤال
     */
    public function destroy(Request $request)
    {
        $question = QuestionAnswer::findOrFail($request->question_id);
        $question->delete();

        return response()->json([
            'success' => true,
            'message' => 'تم حذف السؤال بنجاح'
        ]);
    }

    /**
     * عرض تفاصيل سؤال
     */
    public function show($id)
    {
        $question = QuestionAnswer::with(['user', 'content'])->findOrFail($id);
        
        return response()->json([
            'success' => true,
            'question' => $question
        ]);
    }

    /**
     * إحصائيات الأسئلة
     */
    public function statistics()
    {
        $stats = [
            'total' => QuestionAnswer::count(),
            'active' => QuestionAnswer::where('status', 1)->count(),
            'inactive' => QuestionAnswer::where('status', 0)->count(),
            'ai_generated' => QuestionAnswer::where('is_ai_generated', 1)->count(),
            'manual' => QuestionAnswer::where('is_ai_generated', 0)->count(),
        ];

        return response()->json([
            'success' => true,
            'statistics' => $stats
        ]);
    }
}
