<?php

namespace App\Services;

use App\Enums\StatusCodes;
use App\Helpers\ServiceResult;
use App\Models\AiPermission;
use App\Models\AiUsageLog;
use App\Models\Category;
use App\Models\Content;
use App\Models\QuestionAnswer;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ContentService extends BaseService
{
    use AuthorizesRequests;

    protected ImageService $imageService;

    public function __construct(ImageService $imageService)
    {
        $this->imageService = $imageService;
    }

    // public function createContent(array $data, array $files): ServiceResult
    // {
    //     $serviceResult = new ServiceResult();
    //     $serviceResult->isSuccess = false;
    //     $serviceResult->data = [];
    //     $serviceResult->status = StatusCodes::OPERATION_FAILED;

    //     DB::beginTransaction();

    //     try {
    //         // إنشاء محتوى جديد باستخدام البيانات المقدمة
    //         $content = Content::create($data);

    //         // إضافة ملفات الميديا إلى مجموعات مختلفة باستخدام مكتبة Spatie MediaLibrary
    //         if (isset($files['classMedia'])) {
    //             $content->addMedia($files['classMedia'])->toMediaCollection('class_media');
    //         }

    //         if (isset($files['teacherMedia'])) {
    //             $content->addMedia($files['teacherMedia'])->toMediaCollection('teacher_media');
    //         }

    //         if (isset($files['studentMedia'])) {
    //             $content->addMedia($files['studentMedia'])->toMediaCollection('student_media');
    //         }

    //         // التحقق مما إذا تم إنشاء المحتوى بنجاح
    //         if ($content) {
    //             $serviceResult->isSuccess = true;
    //             $serviceResult->data = $content;
    //             $serviceResult->status = StatusCodes::OPERATION_SUCCEEDED;
    //         }

    //         DB::commit();
    //     } catch (\Exception $e) {
    //         DB::rollBack();
    //         // يمكنك تسجيل الخطأ إذا لزم الأمر
    //     }

    //     return $serviceResult;
    // }
    public function createContent(array $data): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->isSuccess = false;
        $serviceResult->data = [];
        $serviceResult->status = StatusCodes::OPERATION_FAILED;

        DB::beginTransaction();
        // dd($data['category_id']);
        $cat = Category::find($data['category_id']);
        $existContent = $cat->contents()->first();
        if (!$existContent) {
            try {
                $content = Content::create($data);

                if ($content) {
                    $serviceResult->isSuccess = true;
                    $serviceResult->data = $content;
                    $serviceResult->status = StatusCodes::OPERATION_SUCCEEDED;
                }

                DB::commit();
            } catch (\Exception $e) {
                DB::rollBack();
            }
        } else {
            try {
                $existContent->fill($data);

                if ($existContent->save()) {
                    $serviceResult->isSuccess = true;
                    $serviceResult->data = $existContent;
                    $serviceResult->status = StatusCodes::OPERATION_SUCCEEDED;
                } else {
                    $serviceResult->status = StatusCodes::OPERATION_FAILED;
                }

                DB::commit();
            } catch (\Exception $e) {
                DB::rollBack();
            }
        }

        return $serviceResult;
    }

    public function allContents($itemsPerPage, $trashed = false, $orderBy = 'id', $sortBy = 'asc'): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->isSuccess = false;
        $serviceResult->data = [];
        $serviceResult->status = StatusCodes::OPERATION_FAILED;
        /**
         * @var Content
         */
        $query = Content::query();

        if ($trashed) {
            $query->onlyTrashed();
        }

        $contents = $query->orderBy($orderBy, $sortBy)
            ->paginate($itemsPerPage);

        $serviceResult->isSuccess = true;
        $serviceResult->data = $contents;
        $serviceResult->status = StatusCodes::OPERATION_SUCCEEDED;

        return $serviceResult;
    }

    public function singleContent($contentId): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->isSuccess = false;
        $serviceResult->data = [];
        $serviceResult->status = StatusCodes::AUTHENTICATION_FAILED;

        $content = Content::findOrFail($contentId);

        if ($content) {
            $serviceResult->isSuccess = true;
            $serviceResult->data = $content;
            $serviceResult->status = StatusCodes::OPERATION_SUCCEEDED;
        }

        return $serviceResult;
    }

    public function searchContents($term, $itemsPerPage): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->isSuccess = false;
        $serviceResult->data = [];
        $serviceResult->status = StatusCodes::AUTHENTICATION_FAILED;

        $contents = Content::select()->where(function ($query) use ($term) {
            $query->where('name', 'like', "%$term%");
        })->paginate($itemsPerPage);

        if ($contents->count() > 0) {
            $serviceResult->isSuccess = true;
            $serviceResult->data = $contents;
            $serviceResult->status = StatusCodes::OPERATION_SUCCEEDED;
        }

        return $serviceResult;
    }

    public function updateContent($contentId, $contentData)
    {
        $serviceResult = new ServiceResult();
        $serviceResult->isSuccess = false;
        $serviceResult->data = [];
        $serviceResult->status = StatusCodes::OPERATION_FAILED;

        DB::beginTransaction();

        try {
            $content = Content::findOrFail($contentId);

            $content->fill($contentData);

            if ($content->save()) {
                $serviceResult->isSuccess = true;
                $serviceResult->data = $content;
                $serviceResult->status = StatusCodes::OPERATION_SUCCEEDED;
            } else {
                $serviceResult->status = StatusCodes::OPERATION_FAILED;
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
        }

        return $serviceResult;
    }

    public function deleteContent($contentId): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->isSuccess = false;
        $serviceResult->data = [];
        $serviceResult->status = StatusCodes::AUTHENTICATION_FAILED;

        $content = Content::findOrFail($contentId);
        // $this->authorize('delete', $content);

        if (!$content) {
            $serviceResult->status = StatusCodes::DATA_NOT_FOUND;

            return $serviceResult;
        }

        if ($content->delete()) {
            $serviceResult->isSuccess = true;
            $serviceResult->data = $content;
            $serviceResult->status = StatusCodes::OPERATION_SUCCEEDED;
        } else {
            $serviceResult->status = StatusCodes::OPERATION_FAILED;
        }

        return $serviceResult;
    }

    public function restoreContent($contentId): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->isSuccess = false;
        $serviceResult->data = [];
        $serviceResult->status = StatusCodes::AUTHENTICATION_FAILED;

        $content = Content::onlyTrashed()->findOrFail($contentId);
        // $this->authorize('restore', $content);

        if (!$content) {
            $serviceResult->status = StatusCodes::DATA_NOT_FOUND;

            return $serviceResult;
        }

        if ($content->restore()) {
            $serviceResult->isSuccess = true;
            $serviceResult->data = $content;
            $serviceResult->status = StatusCodes::OPERATION_SUCCEEDED;
        } else {
            $serviceResult->status = StatusCodes::OPERATION_FAILED;
        }

        return $serviceResult;
    }

    public function forceDeleteContent($contentId): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->isSuccess = false;
        $serviceResult->data = [];
        $serviceResult->status = StatusCodes::AUTHENTICATION_FAILED;

        $content = Content::onlyTrashed()->findOrFail($contentId);
        // $this->authorize('forceDelete', $content);

        if (!$content) {
            $serviceResult->status = StatusCodes::DATA_NOT_FOUND;

            return $serviceResult;
        }

        if ($content->forceDelete()) {
            $serviceResult->isSuccess = true;
            $serviceResult->data = $content;
            $serviceResult->status = StatusCodes::OPERATION_SUCCEEDED;
        } else {
            $serviceResult->status = StatusCodes::OPERATION_FAILED;
        }

        return $serviceResult;
    }

    /**
     * إنشاء محتوى جديد مع معالجة الصور بكفاءة.
     */
    public function createContentWithMedia(array $data, array $files = []): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->isSuccess = false;
        $serviceResult->data = [];
        $serviceResult->status = StatusCodes::OPERATION_FAILED;

        DB::beginTransaction();

        try {
            $cat = Category::find($data['category_id']);
            $existContent = $cat->contents()->first();

            if (!$existContent) {
                $content = Content::create($data);
            } else {
                $content = $existContent;
                $content->fill($data);
                $content->save();
            }

            // معالجة الملفات المرفوعة
            if (!empty($files)) {
                $this->processMediaFiles($content, $files);
            }

            $serviceResult->isSuccess = true;
            $serviceResult->data = $content->load('media');
            $serviceResult->status = StatusCodes::OPERATION_SUCCEEDED;

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Content creation error: '.$e->getMessage());
        }

        return $serviceResult;
    }

    /**
     * معالجة مفات الوسائط.
     */
    private function processMediaFiles(Content $content, array $files): void
    {
        foreach ($files as $collectionName => $fileArray) {
            if (!is_array($fileArray)) {
                $fileArray = [$fileArray];
            }

            foreach ($fileArray as $file) {
                if ($file instanceof UploadedFile) {
                    // التحقق من صحة الصورة
                    $validationErrors = $this->imageService->validateImage($file);
                    if (!empty($validationErrors)) {
                        throw new \Exception(implode(', ', $validationErrors));
                    }

                    // إضافة الملف إلى المجموعة المناسبة
                    $content->addMedia($file)->toMediaCollection($collectionName);
                }
            }
        }
    }

    /**
     * الحصول على الصور بصيغة base64 للذكاء الاصطناعي.
     */
    public function getImagesForAI(Content $content, string $collectionName): array
    {
        $images = [];
        $media = $content->getMedia($collectionName);

        foreach ($media as $item) {
            if (strpos($item->mime_type, 'image/') === 0) {
                $base64 = $this->imageService->getBase64ForAI($item->getPath());
                if ($base64) {
                    $images[] = [
                        'id' => $item->id,
                        'mime_type' => $item->mime_type,
                        'base64' => $base64,
                        'name' => $item->name,
                    ];
                }
            }
        }

        return $images;
    }
    // For Apis

    public function contentsIndex($category, $itemsPerPage, $orderBy = 'id', $sortBy = 'asc'): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->isSuccess = false;
        $serviceResult->data = [];
        $serviceResult->status = StatusCodes::OPERATION_FAILED;
        /**
         * @var Content
         */
        $query = Content::query();
        if ($category) {
            $query = $query->where('category_id', $category);
        }

        $contents = $query->orderBy($orderBy, $sortBy)
            ->paginate($itemsPerPage);
        $serviceResult->isSuccess = true;
        $serviceResult->data = $contents;
        $serviceResult->status = StatusCodes::OPERATION_SUCCEEDED;

        return $serviceResult;
    }

    protected function checkPermission(string $permission): bool
    {
        $user = Auth::user();

        // استثناء المستخدم ذو الرول رقم 1
        if ($user->role_id === 1) {
            return true;
        }

        // التحقق من صلاحيات المعلم
        if ($user->type === 2) {
            $permissions = AiPermission::where(function ($query) use ($user) {
                $query->where('teacher_id', $user->id)
                    ->orWhere('school_id', $user->school_id);
            })->first();

            if ($permissions && $permissions->$permission) {
                return true;
            }
        }

        return false;
    }

    /**
     * الحصول على جميع أسئلة المحتوى.
     */
    public function getContentQuestions($contentId): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->isSuccess = false;
        $serviceResult->data = [];
        $serviceResult->status = StatusCodes::OPERATION_FAILED;

        try {
            if (!$this->checkPermission('can_view_questions')) {
                throw new \Exception('ليس لديك صلاحية لعرض الأسئلة');
            }

            $content = Content::findOrFail($contentId);

            // جلب الأسئلة حسب نوع المستخدم
            $user = Auth::user();
            if ($user->type === 1) { // أدمن - يرى جميع الأسئلة
                $questions = $content->questionsAnswers()->orderBy('created_at', 'desc')->get();
            } else { // معلم - يرى أسئلته الخاصة + الأسئلة المفعلة من الآخرين
                $questions = $content->questionsAnswers()
                    ->where(function($query) use ($user) {
                        $query->where('user_id', $user->id) // أسئلته الخاصة (مفعلة وغير مفعلة)
                              ->orWhere('status', 1); // أو الأسئلة المفعلة من الآخرين
                    })
                    ->orderBy('created_at', 'desc')
                    ->get();
            }

            $serviceResult->isSuccess = true;
            $serviceResult->data = $questions;
            $serviceResult->status = StatusCodes::OPERATION_SUCCEEDED;
        } catch (\Exception $e) {
            $serviceResult->message = $e->getMessage();
        }

        return $serviceResult;
    }

    /**
     * إضافة سؤال جديد.
     */
    public function addQuestion($contentId, $data): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->isSuccess = false;
        $serviceResult->data = [];
        $serviceResult->status = StatusCodes::OPERATION_FAILED;

        DB::beginTransaction();

        try {
            if (!$this->checkPermission('can_add_questions')) {
                throw new \Exception('ليس لديك صلاحية لإضافة الأسئلة');
            }

            // فحص الحد اليومي للمعلمين
            $user = Auth::user();
            if ($user->type === 2) { // معلم
                $permission = AiPermission::where(function ($query) use ($user) {
                    $query->where('teacher_id', $user->id)
                        ->orWhere('school_id', $user->school_id);
                })->first();

                if ($permission && $permission->questions_limit > 0) {
                    if (!AiUsageLog::canPerformAction($user->id, 'add_questions', $permission->questions_limit)) {
                        $todayUsage = AiUsageLog::getTodayUsage($user->id, 'add_questions');
                        throw new \Exception("تم استنفاد الحد اليومي ({$todayUsage}/{$permission->questions_limit})");
                    }
                }
            }

            $content = Content::findOrFail($contentId);

            $questionData = array_merge($data, [
                'user_id' => $user->id,
                'content_id' => $contentId,
                'difficulty' => $data['difficulty'] ?? 'متوسط',
                'type' => $data['type'] ?? 'عام',
                'status' => $user->type === 2 ? 0 : 1, // المعلمون: غير مفعل، الأدمن: مفعل
                'is_ai_generated' => $data['is_ai_generated'] ?? false,
            ]);

            $question = $content->questionsAnswers()->create($questionData);

            // تسجيل الاستخدام للمعلمين
            if ($user->type === 2) { // معلم
                AiUsageLog::logUsage($user->id, 'add_questions');
            }

            $serviceResult->isSuccess = true;
            $serviceResult->data = $question;
            $serviceResult->status = StatusCodes::OPERATION_SUCCEEDED;

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $serviceResult->message = $e->getMessage();
        }

        return $serviceResult;
    }

    /**
     * تعديل سؤال موجود.
     */
    public function editQuestion($questionId, $data): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->isSuccess = false;
        $serviceResult->data = [];
        $serviceResult->status = StatusCodes::OPERATION_FAILED;

        DB::beginTransaction();

        try {
            if (!$this->checkPermission('can_edit_questions')) {
                throw new \Exception('ليس لديك صلاحية لتعديل الأسئلة');
            }

            $question = QuestionAnswer::findOrFail($questionId);
            $question->update($data);

            $serviceResult->isSuccess = true;
            $serviceResult->data = $question->fresh();
            $serviceResult->status = StatusCodes::OPERATION_SUCCEEDED;

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $serviceResult->message = $e->getMessage();
        }

        return $serviceResult;
    }

    /**
     * حذف سؤال.
     */
    public function deleteQuestion($questionId): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->isSuccess = false;
        $serviceResult->data = [];
        $serviceResult->status = StatusCodes::OPERATION_FAILED;

        DB::beginTransaction();

        try {
            if (!$this->checkPermission('can_delete_questions')) {
                throw new \Exception('ليس لديك صلاحية لحذف الأسئلة');
            }

            $question = QuestionAnswer::findOrFail($questionId);
            $question->delete();

            $serviceResult->isSuccess = true;
            $serviceResult->data = null;
            $serviceResult->status = StatusCodes::OPERATION_SUCCEEDED;

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $serviceResult->message = $e->getMessage();
        }

        return $serviceResult;
    }

    /**
     * توليد إجابة بالذكاء الاصطناعي
     */
    public function ai_answer($questionId): ServiceResult
    {
        $serviceResult = new ServiceResult();
        $serviceResult->isSuccess = false;
        $serviceResult->data = [];
        $serviceResult->status = StatusCodes::OPERATION_FAILED;

        try {
            if (!$this->checkPermission('can_generate_answers')) {
                throw new \Exception('ليس لديك صلاحية لتوليد الإجابات');
            }

            $question = QuestionAnswer::findOrFail($questionId);
            $content = Content::findOrFail($question->content_id);

            // الحصول على محتوى المعلم للسياق
            $description = json_decode($content->description, true);
            $textContent = '';

            if ($description && !empty($description['teacher'])) {
                $textContent = strip_tags($description['teacher']);
            }

            // إنشاء prompt لتوليد الإجابة
            $prompt = "أنت مساعد تعليمي ذكي. بناءً على المحتوى التعليمي التالي، قدم إجابة شاملة ومفيدة للسؤال:\n\n";

            if (!empty($textContent)) {
                $prompt .= "المحتوى التعليمي:\n" . trim($textContent) . "\n\n";
            }

            $prompt .= 'السؤال: ' . $question->question . "\n\n";
            $prompt .= 'قدم إجابة واضحة ومفصلة ومناسبة للمستوى التعليمي.';

            $answer = $this->callGeminiAPI($prompt, 600);

            if ($answer && $answer !== 'فشل في الاتصال بخدمة التحليل. يرجى المحاولة لاحقاً.') {
                // تحديث السؤال بالإجابة المولدة
                $question->update(['answer' => $answer]);

                $serviceResult->isSuccess = true;
                $serviceResult->data = $question->fresh();
                $serviceResult->status = StatusCodes::OPERATION_SUCCEEDED;
            } else {
                throw new \Exception('فشل في توليد الإجابة');
            }
        } catch (\Exception $e) {
            $serviceResult->message = $e->getMessage();
        }

        return $serviceResult;
    }

    /**
     * استدعاء Gemini API
     */
    private function callGeminiAPI($prompt, $maxTokens = 400, $retries = 3)
    {
        $payload = [
            'contents' => [['parts' => [['text' => $prompt]]]],
            'generationConfig' => ['temperature' => 0.3, 'maxOutputTokens' => $maxTokens],
        ];

        return $this->callGeminiAPIWithPayload($payload, 25, $retries);
    }

    /**
     * استدعاء Gemini API مع payload
     */
    private function callGeminiAPIWithPayload($payload, $timeout = 25, $retries = 2)
    {
        $apiKey = env('GEMINI_API_KEY');
        $url = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=' . $apiKey;

        for ($attempt = 1; $attempt <= $retries; ++$attempt) {
            try {
                $response = \Illuminate\Support\Facades\Http::timeout($timeout)->post($url, $payload);

                if ($response->successful()) {
                    $responseData = $response->json();
                    return $responseData['candidates'][0]['content']['parts'][0]['text'] ?? null;
                }

                $error = $response->json();

                if (isset($error['error']['code']) && $error['error']['code'] == 429) {
                    \Log::error('Gemini API quota exceeded: ' . json_encode($error));
                    return 'تم استنفاد الحد اليومي لخدمة التحليل. يرجى المحاولة غداً.';
                }

                if (isset($error['error']['code']) && $error['error']['code'] == 503 && $attempt < $retries) {
                    \Log::info("Gemini API overloaded, retrying attempt {$attempt}/{$retries}");
                    sleep(2);
                    continue;
                }

                \Log::warning('Gemini API failed: ' . json_encode($error));
                return 'فشل في الاتصال بخدمة التحليل.';
            } catch (\Exception $e) {
                \Log::error("Gemini API exception attempt {$attempt}: " . $e->getMessage());
                if ($attempt < $retries) {
                    sleep(1);
                    continue;
                }
                return 'خطأ في الاتصال بخدمة التحليل.';
            }
        }

        return 'فشل في الاتصال بخدمة التحليل.';
    }
}
