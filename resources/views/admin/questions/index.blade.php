@extends('layouts.app')

@section('title', 'إدارة الأسئلة')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">إدارة الأسئلة</h3>
                    <button class="btn btn-info" onclick="loadStatistics()">
                        <i class="fas fa-chart-bar"></i> الإحصائيات
                    </button>
                </div>

                <!-- فلاتر البحث -->
                <div class="card-body">
                    <form method="GET" action="{{ route('admin.questions.index') }}" class="row g-3 mb-4">
                        <div class="col-md-3">
                            <label class="form-label">الحالة</label>
                            <select name="status" class="form-select">
                                <option value="">جميع الحالات</option>
                                <option value="1" {{ request('status') == '1' ? 'selected' : '' }}>مفعل</option>
                                <option value="0" {{ request('status') == '0' ? 'selected' : '' }}>غير مفعل</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">المعلم</label>
                            <select name="teacher_id" class="form-select">
                                <option value="">جميع المعلمين</option>
                                @foreach($teachers as $teacher)
                                    <option value="{{ $teacher->id }}" {{ request('teacher_id') == $teacher->id ? 'selected' : '' }}>
                                        {{ $teacher->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">النوع</label>
                            <select name="type" class="form-select">
                                <option value="">جميع الأنواع</option>
                                <option value="ai" {{ request('type') == 'ai' ? 'selected' : '' }}>ذكاء اصطناعي</option>
                                <option value="manual" {{ request('type') == 'manual' ? 'selected' : '' }}>يدوي</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> بحث
                                </button>
                                <a href="{{ route('admin.questions.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> إلغاء
                                </a>
                            </div>
                        </div>
                    </form>

                    <!-- أزرار العمليات المجمعة -->
                    <div class="mb-3">
                        <button class="btn btn-success" onclick="bulkActivate()">
                            <i class="fas fa-check"></i> تفعيل المحدد
                        </button>
                        <button class="btn btn-warning" onclick="bulkDeactivate()">
                            <i class="fas fa-times"></i> إلغاء تفعيل المحدد
                        </button>
                        <button class="btn btn-danger" onclick="bulkDelete()">
                            <i class="fas fa-trash"></i> حذف المحدد
                        </button>
                    </div>

                    <!-- جدول الأسئلة -->
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>
                                        <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                    </th>
                                    <th>السؤال</th>
                                    <th>المعلم</th>
                                    <th>النوع</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($questions as $question)
                                    <tr>
                                        <td>
                                            <input type="checkbox" class="question-checkbox" value="{{ $question->id }}">
                                        </td>
                                        <td>
                                            <div class="question-preview" style="max-width: 300px;">
                                                {{ Str::limit($question->question, 100) }}
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ $question->user->name ?? 'غير محدد' }}</span>
                                        </td>
                                        <td>
                                            @if($question->is_ai_generated)
                                                <span class="badge bg-primary">ذكاء اصطناعي</span>
                                            @else
                                                <span class="badge bg-secondary">يدوي</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($question->status)
                                                <span class="badge bg-success">مفعل</span>
                                            @else
                                                <span class="badge bg-warning">غير مفعل</span>
                                            @endif
                                        </td>
                                        <td>{{ $question->created_at->format('Y-m-d H:i') }}</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-sm btn-info" onclick="showQuestion({{ $question->id }})">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                @if($question->status)
                                                    <button class="btn btn-sm btn-warning" onclick="deactivateQuestion({{ $question->id }})">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                @else
                                                    <button class="btn btn-sm btn-success" onclick="activateQuestion({{ $question->id }})">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                @endif
                                                <button class="btn btn-sm btn-danger" onclick="deleteQuestion({{ $question->id }})">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="7" class="text-center">لا توجد أسئلة</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center">
                        {{ $questions->appends(request()->query())->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal لعرض تفاصيل السؤال -->
<div class="modal fade" id="questionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل السؤال</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="questionDetails">
                <!-- سيتم تحميل التفاصيل هنا -->
            </div>
        </div>
    </div>
</div>

<!-- Modal للإحصائيات -->
<div class="modal fade" id="statisticsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إحصائيات الأسئلة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="statisticsContent">
                <!-- سيتم تحميل الإحصائيات هنا -->
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
// تحديد/إلغاء تحديد جميع الأسئلة
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.question-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}

// الحصول على الأسئلة المحددة
function getSelectedQuestions() {
    const checkboxes = document.querySelectorAll('.question-checkbox:checked');
    return Array.from(checkboxes).map(cb => cb.value);
}

// تفعيل سؤال واحد
function activateQuestion(questionId) {
    if (confirm('هل أنت متأكد من تفعيل هذا السؤال؟')) {
        fetch('{{ route("admin.questions.activate") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: JSON.stringify({ question_id: questionId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء تفعيل السؤال');
            }
        });
    }
}

// إلغاء تفعيل سؤال واحد
function deactivateQuestion(questionId) {
    if (confirm('هل أنت متأكد من إلغاء تفعيل هذا السؤال؟')) {
        fetch('{{ route("admin.questions.deactivate") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: JSON.stringify({ question_id: questionId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء إلغاء تفعيل السؤال');
            }
        });
    }
}

// حذف سؤال واحد
function deleteQuestion(questionId) {
    if (confirm('هل أنت متأكد من حذف هذا السؤال؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        fetch('{{ route("admin.questions.destroy") }}', {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: JSON.stringify({ question_id: questionId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء حذف السؤال');
            }
        });
    }
}

// تفعيل عدة أسئلة
function bulkActivate() {
    const selectedQuestions = getSelectedQuestions();
    if (selectedQuestions.length === 0) {
        alert('يرجى تحديد أسئلة للتفعيل');
        return;
    }
    
    if (confirm(`هل أنت متأكد من تفعيل ${selectedQuestions.length} سؤال؟`)) {
        fetch('{{ route("admin.questions.bulk.activate") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: JSON.stringify({ question_ids: selectedQuestions })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء تفعيل الأسئلة');
            }
        });
    }
}

// إلغاء تفعيل عدة أسئلة
function bulkDeactivate() {
    const selectedQuestions = getSelectedQuestions();
    if (selectedQuestions.length === 0) {
        alert('يرجى تحديد أسئلة لإلغاء التفعيل');
        return;
    }
    
    if (confirm(`هل أنت متأكد من إلغاء تفعيل ${selectedQuestions.length} سؤال؟`)) {
        fetch('{{ route("admin.questions.bulk.deactivate") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: JSON.stringify({ question_ids: selectedQuestions })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء إلغاء تفعيل الأسئلة');
            }
        });
    }
}

// عرض تفاصيل السؤال
function showQuestion(questionId) {
    fetch(`{{ route("admin.questions.show", ":id") }}`.replace(':id', questionId))
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const question = data.question;
                document.getElementById('questionDetails').innerHTML = `
                    <div class="row">
                        <div class="col-md-6">
                            <strong>المعلم:</strong> ${question.user ? question.user.name : 'غير محدد'}
                        </div>
                        <div class="col-md-6">
                            <strong>النوع:</strong> ${question.is_ai_generated ? 'ذكاء اصطناعي' : 'يدوي'}
                        </div>
                        <div class="col-md-6">
                            <strong>الصعوبة:</strong> ${question.difficulty}
                        </div>
                        <div class="col-md-6">
                            <strong>الحالة:</strong> ${question.status ? 'مفعل' : 'غير مفعل'}
                        </div>
                        <div class="col-12 mt-3">
                            <strong>السؤال:</strong>
                            <div class="border p-3 mt-2">${question.question}</div>
                        </div>
                        <div class="col-12 mt-3">
                            <strong>الإجابة:</strong>
                            <div class="border p-3 mt-2">${question.answer || 'لا توجد إجابة'}</div>
                        </div>
                    </div>
                `;
                new bootstrap.Modal(document.getElementById('questionModal')).show();
            }
        });
}

// تحميل الإحصائيات
function loadStatistics() {
    fetch('{{ route("admin.questions.statistics") }}')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const stats = data.statistics;
                document.getElementById('statisticsContent').innerHTML = `
                    <div class="row text-center">
                        <div class="col-md-6 mb-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <h3>${stats.total}</h3>
                                    <p>إجمالي الأسئلة</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <h3>${stats.active}</h3>
                                    <p>الأسئلة المفعلة</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <h3>${stats.inactive}</h3>
                                    <p>الأسئلة غير المفعلة</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <h3>${stats.ai_generated}</h3>
                                    <p>أسئلة الذكاء الاصطناعي</p>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                new bootstrap.Modal(document.getElementById('statisticsModal')).show();
            }
        });
}
</script>
@endsection
