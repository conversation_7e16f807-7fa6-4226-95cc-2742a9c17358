<!-- Modal للأسئلة والأجوبة -->
<div class="modal fade" id="questionsModal" tabindex="-1" aria-labelledby="questionsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="questionsModalLabel">
                    <i class="fas fa-question-circle"></i> إدارة الأسئلة والأجوبة
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <!-- أزرار التحكم -->
                <div class="d-flex justify-content-between mb-3">
                    <button class="btn btn-success" onclick="showAddQuestionForm()">
                        <i class="fas fa-plus"></i> إضافة سؤال جديد
                    </button>
                    <button class="btn btn-info" onclick="generateSuggestions()">
                        <i class="fas fa-lightbulb"></i> اقتراحات الذكاء الاصطناعي
                    </button>
                </div>

                <!-- نموذج إضافة سؤال -->
                <div id="addQuestionForm" class="card mb-3" style="display: none;">
                    <div class="card-header">
                        <h6><i class="fas fa-edit"></i> إضافة سؤال جديد</h6>
                    </div>
                    <div class="card-body">
                        <form id="questionForm">
                            <div class="mb-3">
                                <label class="form-label">السؤال</label>
                                <textarea class="form-control" id="questionText" rows="2" required></textarea>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">الإجابة</label>
                                <textarea class="form-control" id="answerText" rows="3" required></textarea>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">مستوى الصعوبة</label>
                                <select class="form-select" id="difficulty">
                                    <option value="سهل">سهل</option>
                                    <option value="متوسط" selected>متوسط</option>
                                    <option value="صعب">صعب</option>
                                </select>
                            </div>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> حفظ السؤال
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="hideAddQuestionForm()">
                                    إلغاء
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- منطقة الاقتراحات -->
                <div id="suggestionsArea" class="alert alert-info" style="display: none;">
                    <h6><i class="fas fa-robot"></i> اقتراحات الذكاء الاصطناعي:</h6>
                    <div id="suggestionsContent"></div>
                </div>

                <!-- قائمة الأسئلة -->
                <div id="questionsList">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let currentContentId = null;

function openQuestionsModal(contentId) {
    currentContentId = contentId;
    $('#questionsModal').modal('show');
    loadQuestions();
}

function loadQuestions() {
    if (!currentContentId) return;
    
    fetch(`/questions/${currentContentId}`)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                document.getElementById('questionsList').innerHTML = 
                    `<div class="alert alert-danger">${data.error}</div>`;
                return;
            }
            displayQuestions(data.questions);
            updateUsageInfo(data.usage_info);
        })
        .catch(error => {
            console.error('Error loading questions:', error);
            document.getElementById('questionsList').innerHTML = 
                '<div class="alert alert-danger">خطأ في تحميل الأسئلة</div>';
        });
}

function updateUsageInfo(usageInfo) {
    if (!usageInfo) return;
    
    const usageDiv = document.getElementById('usageInfo') || createUsageDiv();
    
    if (usageInfo.limit > 0) {
        usageDiv.innerHTML = `
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> 
                الاستخدام اليومي: ${usageInfo.today_usage}/${usageInfo.limit}
                ${usageInfo.remaining >= 0 ? `(متبقي: ${usageInfo.remaining})` : ''}
            </div>
        `;
    } else {
        usageDiv.innerHTML = `
            <div class="alert alert-success">
                <i class="fas fa-infinity"></i> استخدام غير محدود
            </div>
        `;
    }
}

function createUsageDiv() {
    const usageDiv = document.createElement('div');
    usageDiv.id = 'usageInfo';
    usageDiv.className = 'mb-3';
    document.querySelector('.modal-body').insertBefore(usageDiv, document.getElementById('addQuestionForm'));
    return usageDiv;
}

function displayQuestions(questions) {
    const container = document.getElementById('questionsList');
    
    if (questions.length === 0) {
        container.innerHTML = '<div class="alert alert-warning">لا توجد أسئلة حتى الآن</div>';
        return;
    }
    
    let html = '<div class="row">';
    questions.forEach((q, index) => {
        const difficultyClass = {
            'سهل': 'success',
            'متوسط': 'warning', 
            'صعب': 'danger'
        }[q.difficulty] || 'secondary';
        
        html += `
            <div class="col-md-6 mb-3">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <span class="badge bg-${difficultyClass}">${q.difficulty}</span>
                        <small class="text-muted">${q.is_ai_generated ? 'ذكاء اصطناعي' : 'يدوي'}</small>
                    </div>
                    <div class="card-body">
                        <h6 class="card-title text-primary">س${index + 1}: ${q.question}</h6>
                        <p class="card-text">${q.answer}</p>
                        <small class="text-muted">النوع: ${q.type || 'عام'}</small>
                    </div>
                </div>
            </div>
        `;
    });
    html += '</div>';
    
    container.innerHTML = html;
}

function showAddQuestionForm() {
    document.getElementById('addQuestionForm').style.display = 'block';
}

function hideAddQuestionForm() {
    document.getElementById('addQuestionForm').style.display = 'none';
    document.getElementById('questionForm').reset();
}

document.getElementById('questionForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
    submitBtn.disabled = true;
    
    const formData = {
        content_id: currentContentId,
        question: document.getElementById('questionText').value,
        answer: document.getElementById('answerText').value,
        difficulty: document.getElementById('difficulty').value
    };
    
    fetch('/add-question', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            hideAddQuestionForm();
            loadQuestions();
            showToast('تم إضافة السؤال بنجاح', 'success');
        } else {
            showToast(data.error || 'خطأ في إضافة السؤال', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('خطأ في إضافة السؤال', 'error');
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
});

function generateSuggestions() {
    if (!currentContentId) return;
    
    const suggestionsArea = document.getElementById('suggestionsArea');
    const suggestionsContent = document.getElementById('suggestionsContent');
    
    suggestionsContent.innerHTML = '<div class="spinner-border spinner-border-sm"></div> جاري إنشاء الاقتراحات...';
    suggestionsArea.style.display = 'block';
    
    fetch('/generate-suggestions', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ content_id: currentContentId })
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            suggestionsContent.innerHTML = `<div class="text-danger">${data.error}</div>`;
        } else if (data.suggestions) {
            suggestionsContent.innerHTML = `<pre class="bg-light p-3 rounded">${data.suggestions}</pre>`;
        } else {
            suggestionsContent.innerHTML = '<div class="text-danger">فشل في إنشاء الاقتراحات</div>';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        suggestionsContent.innerHTML = '<div class="text-danger">خطأ في إنشاء الاقتراحات</div>';
    });
}

function showToast(message, type) {
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(toast);
    
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 3000);
}
</script>