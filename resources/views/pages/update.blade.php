<x-app-layout>
    <style>
        /* ضمان ظهور المودالات بشكل صحيح */
        .modal {
            z-index: 1055 !important;
        }

        .modal-backdrop {
            z-index: 1050 !important;
        }

        /* منع تداخل event listeners */
        .edit-saved-question-btn,
        .delete-saved-question-btn,
        .edit-question-btn,
        .delete-question-btn,
        .save-question-btn {
            pointer-events: auto !important;
            cursor: pointer !important;
        }
    </style>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="category-title-box d-sm-flex align-items-center justify-content-between">
                    <h4 class="mb-sm-0 font-size-18">{{ __('Content') }}</h4>
                    @if (session('alert'))
                        <script>
                            window.dispatchEvent(new CustomEvent('showAlert', {
                                detail: @json(session('alert'))
                            }));
                        </script>
                    @endif

                    <div class="category-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('Dashboard') }}</a></li>
                            <li class="breadcrumb-item active">{{ __('Content') }}</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header"></div>
                    <div class="card-body">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="createModalLabel">{{ __('Update') }} {{ __('Content') }}
                                </h5>
                            </div>
                            <div class="modal-body">
                                <form action="{{ route('content.update') }}" method="POST">
                                    @csrf
                                    <div class="col col-12 mt-2 mb-2">
                                        <input type="hidden" name="id" value="{{ $data->id }}">
                                        <input type="hidden" name="category" value="{{ $id }}">
                                    </div>
                                    <ul class="nav nav-pills mb-3" id="pills-tab" role="tablist">
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link active" id="pills-class-tab" data-bs-toggle="pill"
                                                data-bs-target="#pills-class" type="button" role="tab"
                                                aria-controls="pills-class" aria-selected="true">
                                                {{ __('Class') }}</button>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link" id="pills-teacher-tab" data-bs-toggle="pill"
                                                data-bs-target="#pills-teacher" type="button" role="tab"
                                                aria-controls="pills-teacher" aria-selected="false">
                                                {{ __('Teacher') }}</button>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link" id="pills-student-tab" data-bs-toggle="pill"
                                                data-bs-target="#pills-student" type="button" role="tab"
                                                aria-controls="pills-student" aria-selected="false">
                                                {{ __('Student') }}</button>
                                        </li>
                                    </ul>
                                    <div class="tab-content" id="pills-tabContent">
                                        <div class="tab-pane fade show active" id="pills-class" role="tabpanel"
                                            aria-labelledby="pills-class-tab">
                                            <div class="modal-body">
                                                <div class="progress" style="display: none;">
                                                    <div class="progress-bar" role="progressbar" style="width: 0%;"
                                                        aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%
                                                    </div>
                                                </div>
                                                <!-- تعديل قسم Class -->
                                                <div class="col col-12 dropzone dz-clickable"
                                                    style="max-height: 225px !important; overflow: hidden;">
                                                    <div id="classMediaPreview" class="flex row">
                                                        @if ($data && $data->hasMedia('class_media'))
                                                            @foreach ($data->getMedia('class_media') as $media)
                                                                @php
                                                                    $handred = 100;
                                                                    $size =
                                                                        $handred /
                                                                        count($data->getMedia('class_media'));
                                                                @endphp
                                                                <div class="media-item col">
                                                                    {{-- width="{{ $size }}%"  style="{{ count($data->getMedia('class_media')) > 1 ? 'float:right;' : ''}}"> --}}
                                                                    <a class="delete_media btn btn-danger"
                                                                        href="#"
                                                                        onclick="deleteMedia(event, '{{ route('delete.media') }}', '{{ $data->id }}', 'class_media', '{{ $media->id }}')"
                                                                        data-id="{{ $media->id }}"
                                                                        data-type="class_media"
                                                                        style="position: absolute;top:0%;left:0%;z-index:100"><i
                                                                            class="fa fa-trash"></i></a>
                                                                    @if (strpos($media->mime_type, 'video/') === 0)
                                                                        <video controls width="100%">
                                                                            <source src="{{ $media->getUrl() }}"
                                                                                type="{{ $media->mime_type }}">
                                                                            Your browser does not support the video tag.
                                                                        </video>
                                                                    @else
                                                                        <img src="{{ $media->getUrl() }}"
                                                                            alt="Media Preview" width="100%" />
                                                                    @endif
                                                                </div>
                                                            @endforeach
                                                            <a class="add_more_media btn btn-primary" href="#"
                                                                onclick="triggerFileInput('classMedia')"
                                                                style="position: absolute;top:0%;right:0%;z-index:100"><i
                                                                    class="fa fa-plus"></i> {{ __('Add Media') }}</a>
                                                        @else
                                                            <i class="fa fa-upload w-100 mediaClass"></i>
                                                        @endif
                                                        <div id="classVideoCheckIcon" style="display: none;">
                                                            <img src="{{ asset('assets/png.png') }}" alt="Check Icon"
                                                                style="width: 150px; height: 150px; margin:auto;" />
                                                        </div>
                                                    </div>
                                                    <label for="classMedia">{{ __('media') }}</label>
                                                    <input type="file" id="classMedia"
                                                        data-media-type="class_media"
                                                        style="padding: 6%; cursor: pointer; opacity: 0; position: absolute; top: 15px; left: 0; width: 100%;">
                                                    @error('classMedia')
                                                        <span class="error">{{ $message }}</span>
                                                    @enderror
                                                </div>
                                                <div class="col col-12">
                                                    <label class="form-check-label"
                                                        for="content_class">{{ __('content') }}
                                                        {{ __('Class') }}</label>
                                                    <textarea name="content_class" id="content_class" type="text" class="form-control mt-1">{{ old('content_class', $content['class']) }}</textarea>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="tab-pane fade" id="pills-teacher" role="tabpanel"
                                            aria-labelledby="pills-teacher-tab">
                                            <div class="modal-body">
                                                <div class="progress" style="display: none;">
                                                    <div class="progress-bar" role="progressbar" style="width: 0%;"
                                                        aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%
                                                    </div>
                                                </div>
                                                <!-- تعديل قسم Teacher -->
                                                <div class="col col-12 dropzone dz-clickable"
                                                    style="max-height: 225px !important; overflow: hidden;">
                                                    <div id="teacherMediaPreview" class="flex row">
                                                        @if ($data && $data->hasMedia('teacher_media'))
                                                            @foreach ($data->getMedia('teacher_media') as $media)
                                                                <div class="media-item col">
                                                                    <a class="delete_media btn btn-danger"
                                                                        href="#"
                                                                        onclick="deleteMedia(event, '{{ route('delete.media') }}', '{{ $data->id }}', 'teacher_media', '{{ $media->id }}')"
                                                                        data-id="{{ $media->id }}"
                                                                        data-type="teacher_media"
                                                                        style="position: absolute;top:0%;left:0%;z-index:100"><i
                                                                            class="fa fa-trash"></i></a>
                                                                    @if (strpos($media->mime_type, 'video/') === 0)
                                                                        <video controls width="100%">
                                                                            <source src="{{ $media->getUrl() }}"
                                                                                type="{{ $media->mime_type }}">
                                                                            Your browser does not support the video tag.
                                                                        </video>
                                                                    @else
                                                                        <img src="{{ $media->getUrl() }}"
                                                                            alt="Media Preview" class="w-100" />
                                                                    @endif
                                                                </div>
                                                            @endforeach
                                                            <a class="add_more_media btn btn-primary" href="#"
                                                                onclick="triggerFileInput('teacherMedia')"
                                                                style="position: absolute;top:0%;right:0%;z-index:100"><i
                                                                    class="fa fa-plus"></i> {{ __('Add Media') }}</a>
                                                        @else
                                                            <i class="fa fa-upload w-100 teacher_media"></i>
                                                        @endif
                                                        <div id="teacherVideoCheckIcon" style="display: none;">
                                                            <img src="{{ asset('assets/png.png') }}" alt="Check Icon"
                                                                style="width: 150px; height: 150px; margin:auto;" />
                                                        </div>
                                                    </div>
                                                    <label for="teacherMedia">{{ __('media') }}</label>
                                                    <input type="file" id="teacherMedia"
                                                        data-media-type="teacher_media"
                                                        style="padding: 6%; cursor: pointer; opacity: 0; position: absolute; top: 15px; left: 0; width: 100%;">
                                                    @error('teacherMedia')
                                                        <span class="error">{{ $message }}</span>
                                                    @enderror
                                                </div>
                                                <div class="col col-12">
                                                    <label class="form-check-label"
                                                        for="content_teacher">{{ __('content') }}
                                                        {{ __('Teacher') }}</label>

                                                    <textarea name="content_teacher" id="content_teacher" type="text" class="form-control mt-1">{{ old('content_class', $content['teacher']) }}</textarea>
                                                </div>

                                                @if ((isset($canUseAI) && $canUseAI) || auth()->user()->role_id == 1)
                                                    <div class="col col-12 mt-3">
                                                        <div class="d-grid gap-2">
                                                            <button type="button" id="analyzeWithAI"
                                                                class="btn btn-info">
                                                                <i class="fa fa-robot mr-2"></i>
                                                                @if (isset($hasAnalysis) && $hasAnalysis)
                                                                    إعادة تحليل المحتوى بالذكاء الاصطناعي
                                                                @else
                                                                    تحليل المحتوى بالذكاء الاصطناعي
                                                                @endif
                                                            </button>
                                                            {{--   @if (isset($hasAnalysis) && $hasAnalysis)
                                                            <button type="button" id="reAnalyzeBtn"
                                                                class="btn btn-outline-info">
                                                                <i class="fas fa-redo mr-2"></i> إعادة التحليل مرة أخرى
                                                            </button>
                                                        @endif --}}
                                                        </div>
                                                    </div>
                                                @else
                                                    <div class="col col-12 mt-3">
                                                        <div class="alert alert-warning">
                                                            <i class="fas fa-exclamation-triangle"></i>
                                                            <strong>تنبيه:</strong> ليس لديك صلاحية لاستخدام ميزات
                                                            الذكاء الاصطناعي. يرجى التواصل مع الإدارة.
                                                        </div>
                                                    </div>
                                                @endif

                                                <!-- 1. منطقة عرض التحليل -->
                                                <div class="col col-12 mt-4" id="aiAnalysisSection">
                                                    @if (isset($hasAnalysis) && $hasAnalysis && $data->aiAnalysis)
                                                        <div class="card">
                                                            <div
                                                                class="card-header bg-light d-flex justify-content-between align-items-center">
                                                                <h5 class="mb-0"><i
                                                                        class="fas fa-brain text-info"></i> تحليل
                                                                    الذكاء الاصطناعي</h5>
                                                                <span class="badge bg-info">محفوظ</span>
                                                            </div>
                                                            <div class="card-body">
                                                                <div class="analysis-content">
                                                                    {!! $data->aiAnalysis->comprehensive_analysis !!}
                                                                </div>
                                                            </div>
                                                        </div>
                                                    @endif
                                                </div>

                                                <!-- 2. منطقة عرض الأسئلة المحفوظة -->
                                                <div class="col col-12 mt-4" id="savedQuestionsSection">
                                                    <div class="card">
                                                        <div
                                                            class="card-header bg-light d-flex justify-content-between align-items-center">
                                                            <h5 class="mb-0"><i
                                                                    class="fas fa-question-circle text-primary"></i>
                                                                الأسئلة المحفوظة</h5>
                                                            <div>
                                                                <button type="button"
                                                                    class="btn btn-primary btn-sm me-2"
                                                                    id="addManualQuestionBtn">
                                                                    <i class="fas fa-plus"></i> إضافة سؤال يدوي
                                                                </button>
                                                                <span class="badge bg-info"
                                                                    id="questionsCount">{{ $data->questionsAnswers->count() }}
                                                                    سؤال</span>
                                                            </div>
                                                        </div>
                                                        <div class="card-body" id="savedQuestionsContainer">
                                                            @if ($data->questionsAnswers->count() > 0)
                                                                <div id="savedQuestionsList">
                                                                    @foreach ($data->questionsAnswers as $index => $question)
                                                                        <div class="card mb-3 saved-question-card"
                                                                            data-question-id="{{ $question->id }}">
                                                                            <div class="card-body">
                                                                                <div class="row">
                                                                                    <div class="col-md-8">
                                                                                        <div
                                                                                            class="fw-bold text-primary mb-2">
                                                                                            س{{ $index + 1 }}: <span
                                                                                                class="question-text">{{ $question->question }}</span>
                                                                                        </div>
                                                                                        <div class="text-muted mb-2">ج:
                                                                                            <span
                                                                                                class="answer-text">{{ $question->answer }}</span>
                                                                                        </div>
                                                                                        <div>
                                                                                            <span
                                                                                                class="badge bg-{{ $question->difficulty == 'صعب' ? 'danger' : ($question->difficulty == 'متوسط' ? 'warning' : 'success') }} difficulty-badge">{{ $question->difficulty }}</span>
                                                                                            <span
                                                                                                class="badge bg-info ms-1 type-badge">{{ $question->type ?? 'عام' }}</span>
                                                                                            <span
                                                                                                class="badge bg-secondary ms-1">{{ $question->is_ai_generated ? 'ذكاء اصطناعي' : 'يدوي' }}</span>
                                                                                            @if($question->status)
                                                                                                <span class="badge bg-success ms-1 status-badge">مفعل</span>
                                                                                            @else
                                                                                                <span class="badge bg-warning ms-1 status-badge">غير مفعل</span>
                                                                                            @endif
                                                                                            @if($question->user_id && $question->user_id != auth()->id())
                                                                                                <span class="badge bg-primary ms-1">{{ $question->user->name ?? 'معلم' }}</span>
                                                                                            @endif
                                                                                        </div>
                                                                                    </div>
                                                                                    <div class="col-md-4 text-end">
                                                                                        <div class="btn-group-vertical"
                                                                                            role="group">
                                                                                            <button type="button"
                                                                                                class="btn btn-sm btn-outline-primary mb-1 edit-saved-question-btn"
                                                                                                data-question-id="{{ $question->id }}"
                                                                                                data-question-text="{{ $question->question }}"
                                                                                                data-answer-text="{{ $question->answer }}"
                                                                                                data-difficulty="{{ $question->difficulty }}"
                                                                                                data-type="{{ $question->type ?? 'عام' }}">
                                                                                                <i
                                                                                                    class="fas fa-edit"></i>
                                                                                                تعديل
                                                                                            </button>
                                                                                            @if(auth()->user()->type === 1 && $question->user_id && $question->user_id != auth()->id())
                                                                                                @if($question->status)
                                                                                                    <button type="button"
                                                                                                        class="btn btn-sm btn-outline-warning mb-1 toggle-question-status-btn"
                                                                                                        data-question-id="{{ $question->id }}"
                                                                                                        data-current-status="1">
                                                                                                        <i class="fas fa-times"></i>
                                                                                                        إلغاء التفعيل
                                                                                                    </button>
                                                                                                @else
                                                                                                    <button type="button"
                                                                                                        class="btn btn-sm btn-outline-success mb-1 toggle-question-status-btn"
                                                                                                        data-question-id="{{ $question->id }}"
                                                                                                        data-current-status="0">
                                                                                                        <i class="fas fa-check"></i>
                                                                                                        تفعيل السؤال
                                                                                                    </button>
                                                                                                @endif
                                                                                            @endif
                                                                                            <button type="button"
                                                                                                class="btn btn-sm btn-outline-danger delete-saved-question-btn"
                                                                                                data-question-id="{{ $question->id }}">
                                                                                                <i
                                                                                                    class="fas fa-trash"></i>
                                                                                                حذف
                                                                                            </button>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    @endforeach
                                                                </div>
                                                            @else
                                                                <div class="text-center text-muted py-4"
                                                                    id="noQuestionsMessage">
                                                                    <i class="fas fa-question-circle fa-3x mb-3"></i>
                                                                    <p>لا توجد أسئلة محفوظة بعد</p>
                                                                    <small>استخدم زر "تحليل المحتوى بالذكاء الاصطناعي"
                                                                        لإنشاء أسئلة جديدة</small>
                                                                </div>
                                                            @endif
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- 3. منطقة عرض الأسئلة المقترحة -->
                                                <div class="col col-12 mt-4" id="suggestedQuestionsSection"
                                                    style="display: none;">
                                                    <div class="card border-info">
                                                        <div
                                                            class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                                                            <h5 class="mb-0"><i class="fas fa-lightbulb"></i>
                                                                الأسئلة المقترحة من الذكاء الاصطناعي</h5>
                                                            <span class="badge bg-light text-info"
                                                                id="suggestedQuestionsCount">0 سؤال</span>
                                                        </div>
                                                        <div class="card-body" id="suggestedQuestionsContainer">
                                                            <!-- سيتم عرض الأسئلة المقترحة هنا -->
                                                        </div>
                                                    </div>
                                                </div>




                                            </div>
                                        </div>

                                        <div class="tab-pane fade" id="pills-student" role="tabpanel"
                                            aria-labelledby="pills-student-tab">
                                            <div class="modal-body">
                                                <div class="progress" style="display: none;">
                                                    <div class="progress-bar" role="progressbar" style="width: 0%;"
                                                        aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%
                                                    </div>
                                                </div>

                                                <!-- تعديل قسم Student -->
                                                <div class="col col-12 dropzone dz-clickable"
                                                    style="max-height: 225px !important; overflow: hidden;">
                                                    <div id="studentMediaPreview" class="flex row">
                                                        @if ($data && $data->hasMedia('student_media'))
                                                            @foreach ($data->getMedia('student_media') as $media)
                                                                <div class=" col">
                                                                    <a class="delete_media btn btn-danger"
                                                                        href="#"
                                                                        onclick="deleteMedia(event, '{{ route('delete.media') }}', '{{ $data->id }}', 'student_media', '{{ $media->id }}')"
                                                                        data-id="{{ $media->id }}"
                                                                        data-type="student_media"
                                                                        style="position: absolute;top:0%;left:0%;z-index:100"><i
                                                                            class="fa fa-trash"></i></a>
                                                                    @if (strpos($media->mime_type, 'video/') === 0)
                                                                        <video controls width="100%">
                                                                            <source src="{{ $media->getUrl() }}"
                                                                                type="{{ $media->mime_type }}">
                                                                            Your browser does not support the video tag.
                                                                        </video>
                                                                    @else
                                                                        <img src="{{ $media->getUrl() }}"
                                                                            alt="Media Preview" class="w-100" />
                                                                    @endif
                                                                </div>
                                                            @endforeach
                                                            <a class="add_more_media btn btn-primary" href="#"
                                                                onclick="triggerFileInput('studentMedia')"
                                                                style="position: absolute;top:0%;right:0%;z-index:100"><i
                                                                    class="fa fa-plus"></i> {{ __('Add Media') }}</a>
                                                        @else
                                                            <i class="fa fa-upload w-100 student_media"></i>
                                                        @endif
                                                        <div id="studentVideoCheckIcon" style="display: none;">
                                                            <img src="{{ asset('assets/png.png') }}" alt="Check Icon"
                                                                style="width: 150px; height: 150px; margin:auto;" />
                                                        </div>
                                                    </div>
                                                    <label for="studentMedia">{{ __('media') }}</label>
                                                    <input type="file" id="studentMedia"
                                                        data-media-type="student_media"
                                                        style="padding: 6%; cursor: pointer; opacity: 0; position: absolute; top: 15px; left: 0; width: 100%;">
                                                    @error('studentMedia')
                                                        <span class="error">{{ $message }}</span>
                                                    @enderror
                                                </div>
                                                <div class="col col-12">
                                                    <label class="form-check-label"
                                                        for="content_student">{{ __('content') }}
                                                        {{ __('Student') }}</label>
                                                    <textarea name="content_student" id="content_student" type="text" class="form-control mt-1">{{ old('content_student', $content['student']) }}</textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="submit" class="btn btn-primary">{{ __('Save') }}</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <style>
        .mediaClass {
            width: 100% !important;
            text-align: center;
            font-size: 150px;
            padding-top: 40px;
            padding-bottom: 40px;
        }
    </style>
    <style>
        .media-item {
            position: relative;
            display: inline-block;
            margin-top: 36px;
        }

        /* تنسيقات قسم تحليل الذكاء الاصطناعي */
        .ai-analysis {
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 8px;
        }

        .ai-analysis h4 {
            color: #2c3e50;
            margin-bottom: 15px;
            border-bottom: 2px solid #3498db;
            padding-bottom: 8px;
        }

        .analysis-section {
            margin-bottom: 20px;
        }

        .analysis-section h5 {
            color: #3498db;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .analysis-section ul {
            padding-right: 20px;
        }

        .analysis-section li {
            margin-bottom: 5px;
        }

        .text-warning {
            color: #e67e22;
        }

        .text-success {
            color: #27ae60;
        }

        .text-info {
            color: #3498db;
        }
    </style>
    {{-- <script>
        function triggerFileInput(inputId) {
            document.getElementById(inputId).click();
        }
    </script> --}}

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // كود بسيط لزر التحليل
            setTimeout(() => {
                const btn = document.getElementById('analyzeWithAI');
                if (btn) {
                    btn.onclick = function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        Swal.fire({
                            title: 'جاري التحليل...',
                            allowOutsideClick: false,
                            showConfirmButton: false,
                            didOpen: () => Swal.showLoading()
                        });

                        const content = tinymce.get('content_teacher').getContent();
                        const contentId = document.querySelector('input[name="id"]').value;

                        fetch('{{ route('analyze.content') }}', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                                },
                                body: JSON.stringify({
                                    content_id: contentId,
                                    content: content,
                                    media_type: 'teacher_media'
                                })
                            })
                            .then(r => r.json())
                            .then(data => {
                                Swal.close();
                                if (data.success) {
                                    document.getElementById('aiAnalysisSection').innerHTML = data
                                        .analysis;

                                    // ربط أحداث أزرار الأسئلة المقترحة
                                    setTimeout(() => {
                                        // زر حفظ الأسئلة المقترحة
                                        document.querySelectorAll(
                                            '.save-suggested-question-btn').forEach(
                                            btn => {
                                                btn.onclick = function(e) {
                                                    e.preventDefault();
                                                    e.stopPropagation();
                                                    const question = this.dataset
                                                        .question;
                                                    const answer = this.dataset
                                                        .answer;
                                                    const difficulty = this.dataset
                                                        .difficulty || 'متوسط';
                                                    const type = this.dataset
                                                        .type || 'عام';

                                                    Swal.fire({
                                                        title: 'حفظ السؤال',
                                                        text: 'هل تريد حفظ هذا السؤال في قاعدة البيانات؟',
                                                        icon: 'question',
                                                        showCancelButton: true,
                                                        confirmButtonText: 'نعم، احفظ',
                                                        cancelButtonText: 'إلغاء'
                                                    }).then((result) => {
                                                        if (result
                                                            .isConfirmed) {
                                                            this.innerHTML =
                                                                '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
                                                            this.disabled =
                                                                true;

                                                            fetch('{{ route('teacher.add.question') }}', {
                                                                    method: 'POST',
                                                                    headers: {
                                                                        'Content-Type': 'application/json',
                                                                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                                                                    },
                                                                    body: JSON
                                                                        .stringify({
                                                                            content_id: {{ $data->id }},
                                                                            question: question,
                                                                            answer: answer,
                                                                            difficulty: difficulty,
                                                                            type: type,
                                                                            is_ai_generated: true
                                                                        })
                                                                })
                                                                .then(r => r
                                                                    .json())
                                                                .then(
                                                                    data => {
                                                                        if (data
                                                                            .success
                                                                        ) {
                                                                            this.innerHTML =
                                                                                '<i class="fas fa-check"></i> تم الحفظ';
                                                                            this.classList
                                                                                .remove(
                                                                                    'btn-outline-success'
                                                                                );
                                                                            this.classList
                                                                                .add(
                                                                                    'btn-secondary'
                                                                                );
                                                                            Swal.fire(
                                                                                'تم!',
                                                                                'تم حفظ السؤال بنجاح',
                                                                                'success'
                                                                            );
                                                                        } else {
                                                                            this.innerHTML =
                                                                                '<i class="fas fa-save"></i> حفظ';
                                                                            this.disabled =
                                                                                false;
                                                                            Swal.fire(
                                                                                'خطأ',
                                                                                data
                                                                                .message,
                                                                                'error'
                                                                            );
                                                                        }
                                                                    })
                                                                .catch(
                                                                    () => {
                                                                        this.innerHTML =
                                                                            '<i class="fas fa-save"></i> حفظ';
                                                                        this.disabled =
                                                                            false;
                                                                        Swal.fire(
                                                                            'خطأ',
                                                                            'فشل الاتصال',
                                                                            'error'
                                                                        );
                                                                    });
                                                        }
                                                    });
                                                };
                                            });

                                        // زر تعديل الأسئلة المقترحة
                                        document.querySelectorAll(
                                            '.edit-suggested-question-btn').forEach(
                                            btn => {
                                                btn.onclick = function(e) {
                                                    e.preventDefault();
                                                    e.stopPropagation();
                                                    const index = this.dataset
                                                        .index;
                                                    const question = this.dataset
                                                        .question;
                                                    const answer = this.dataset
                                                        .answer;
                                                    const difficulty = this.dataset
                                                        .difficulty;
                                                    const type = this.dataset.type;
                                                    showEditModal('suggested_' +
                                                        index, question, answer,
                                                        difficulty, type,
                                                        'suggested');
                                                };
                                            });

                                        // زر حذف الأسئلة المقترحة
                                        document.querySelectorAll(
                                            '.delete-suggested-question-btn').forEach(
                                            btn => {
                                                btn.onclick = function(e) {
                                                    e.preventDefault();
                                                    e.stopPropagation();
                                                    const index = this.dataset
                                                        .index;
                                                    Swal.fire({
                                                        title: 'حذف السؤال',
                                                        text: 'هل تريد حذف هذا السؤال المقترح؟',
                                                        icon: 'warning',
                                                        showCancelButton: true,
                                                        confirmButtonText: 'نعم، احذف',
                                                        cancelButtonText: 'إلغاء',
                                                        confirmButtonColor: '#dc3545'
                                                    }).then((result) => {
                                                        if (result
                                                            .isConfirmed) {
                                                            const li = this
                                                                .closest(
                                                                    'li');
                                                            li.remove();
                                                            Swal.fire('تم!',
                                                                'تم حذف السؤال المقترح',
                                                                'success'
                                                            );
                                                        }
                                                    });
                                                };
                                            });
                                    }, 500);

                                    Swal.fire('تم!', 'تم التحليل بنجاح', 'success');
                                } else {
                                    Swal.fire('خطأ', data.message, 'error');
                                }
                            })
                            .catch(() => {
                                Swal.close();
                                Swal.fire('خطأ', 'فشل الاتصال', 'error');
                            });
                    };
                    console.log('Button found and event attached');
                } else {
                    console.log('Button not found');
                }
            }, 2000);

            // دائماً استخدام تاب الصف كقيمة افتراضية عند فتح الصفحة
            const activeTab = '#pills-class';
            localStorage.setItem('activeTab', activeTab);

            // إزالة الكلاسات النشطة من جميع التابات أولاً
            document.querySelectorAll('.tab-pane').forEach(tab => {
                tab.classList.remove('show', 'active');
            });

            // تفعيل تاب الصف
            document.querySelector(activeTab).classList.add('show', 'active');

            // تفعيل زر تاب الصف
            document.querySelectorAll('[data-bs-toggle="pill"]').forEach(button => {
                if (button.getAttribute('data-bs-target') === activeTab) {
                    button.classList.add('active');
                } else {
                    button.classList.remove('active');
                }
            });

            document.querySelectorAll('[data-bs-toggle="pill"]').forEach((tabButton) => {
                tabButton.addEventListener('click', function() {
                    localStorage.setItem('activeTab', tabButton.getAttribute('data-bs-target'));
                });
            });

            document.querySelectorAll('input[type="file"]').forEach(input => {
                input.addEventListener('change', function(event) {
                    const previewId = `${input.id}Preview`;
                    const previewElement = document.getElementById(previewId);
                    const progressBar = input.closest('.tab-pane').querySelector('.progress');
                    const progressBarInner = progressBar.querySelector('.progress-bar');

                    // تحديد أيقونة التحقق المناسبة بناءً على نوع الملف
                    const tabType = input.id.replace('Media', '');
                    const videoCheckIcon = document.getElementById(tabType + 'VideoCheckIcon');

                    if (previewElement) {
                        previewElement.innerHTML = ''; // Clear previous content
                        const file = event.target.files[0];

                        if (file) {
                            const fileType = file.type;
                            const reader = new FileReader();

                            // Show progress bar
                            progressBar.style.display = 'block';
                            progressBarInner.style.width = '0%';
                            progressBarInner.setAttribute('aria-valuenow', '0');
                            progressBarInner.textContent = '0%';

                            reader.onprogress = function(e) {
                                if (e.lengthComputable) {
                                    const percentComplete = Math.round((e.loaded / e.total) *
                                        100);
                                    progressBarInner.style.width = `${percentComplete}%`;
                                    progressBarInner.setAttribute('aria-valuenow',
                                        percentComplete.toString());
                                    progressBarInner.textContent = `${percentComplete}%`;
                                }
                            };

                            reader.onload = function(e) {
                                if (fileType.startsWith('image/')) {
                                    const img = document.createElement('img');
                                    img.src = e.target.result;
                                    img.classList.add('w-100');
                                    previewElement.appendChild(img);
                                    if (videoCheckIcon) {
                                        videoCheckIcon.style.display =
                                            'none'; // Hide the check icon for images
                                    }
                                } else if (fileType.startsWith('video/')) {
                                    const video = document.createElement('video');
                                    video.src = URL.createObjectURL(file);
                                    video.controls = true;
                                    video.classList.add('w-100');
                                    previewElement.appendChild(video);

                                    // Display check icon for video files
                                    if (videoCheckIcon) {
                                        videoCheckIcon.style.display = 'block';
                                    }
                                }
                            };

                            reader.readAsDataURL(file);
                        }
                    }
                });
            });
        });
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('input[type="file"]').forEach(input => {
                input.addEventListener('change', function(event) {
                    let fileInput = event.target;
                    let file = fileInput.files[0];
                    if (!file) return; // إذا لم يتم اختيار أي ملف، اخرج من الدالة

                    // تحقق من وجود العنصر الذي يحتوي على شريط التقدم
                    let progressBarContainer = fileInput.closest('.modal-body').querySelector(
                        '.progress');
                    if (!progressBarContainer) {
                        console.error('Progress bar container element not found');
                        return; // إذا لم يتم العثور على عنصر شريط التقدم، اخرج من الدالة
                    }

                    let progressElement = progressBarContainer.querySelector('.progress-bar');
                    if (!progressElement) {
                        console.error('Progress bar element not found');
                        return; // إذا لم يتم العثور على عنصر شريط التقدم، اخرج من الدالة
                    }

                    // إظهار شريط التقدم
                    progressBarContainer.style.display = 'block';
                    progressElement.style.width = '0%';
                    progressElement.textContent = '0%'; // تعيين النص بشكل أولي

                    let form = new FormData();
                    form.append('category', document.querySelector('input[name="category"]')
                        .value); // تأكد من وجود العنصر الذي يحتوي على القيمة
                    form.append('file', file);
                    form.append('media_type', fileInput.getAttribute(
                        'data-media-type')); // استخدام data-media-type بدلاً من id

                    let xhr = new XMLHttpRequest();
                    xhr.open('POST', '/upload', true);
                    xhr.setRequestHeader('X-CSRF-TOKEN', document.querySelector(
                        'meta[name="csrf-token"]').getAttribute('content'));

                    xhr.upload.addEventListener('progress', function(e) {
                        if (e.lengthComputable) {
                            let percentComplete = (e.loaded / e.total) * 100;
                            progressElement.style.width = percentComplete + '%';
                            progressElement.setAttribute('aria-valuenow', percentComplete);
                            progressElement.textContent = Math.round(percentComplete) +
                                '%'; // تعيين النص بناءً على النسبة المئوية
                        }
                    });

                    xhr.onload = function() {
                        if (xhr.status >= 200 && xhr.status < 300) {
                            try {
                                let response = JSON.parse(xhr.responseText);
                                if (response.success) {
                                    progressBarContainer.style.display =
                                        'none'; // إخفاء شريط التقدم بعد النجاح
                                    console.log('File uploaded successfully', response
                                        .file_path);
                                    // تعامل مع النجاح هنا
                                } else {
                                    console.error('Error uploading file', response.message);
                                    // تعامل مع الخطأ هنا
                                }
                            } catch (e) {
                                console.error('Error parsing JSON response:', e);
                                console.log('Response Text:', xhr
                                    .responseText); // عرض نص الاستجابة للتشخيص
                            }
                        } else {
                            console.error('Request failed with status:', xhr.status);
                        }
                    };

                    xhr.onerror = function() {
                        console.error('Request failed');
                    };

                    xhr.send(form);
                });
            });
        });
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            initializeTinyMCE();
        });

        function initializeTinyMCE() {
            tinymce.init({
                selector: '#content_class, #content_teacher, #content_student',
                height: 400,
                plugins: [
                    'advlist autolink link image lists charmap preview anchor',
                    'searchreplace wordcount visualblocks visualchars code fullscreen',
                    'insertdatetime media table emoticons help'
                ],
                toolbar: 'undo redo | formatselect | bold italic backcolor | alignleft aligncenter alignright alignjustify | ' +
                    'bullist numlist outdent indent | removeformat | image | help',
                relative_urls: false,
                remove_script_host: false,
                convert_urls: true,
                document_base_url: '{{ url('/') }}',
                setup: function(editor) {
                    editor.on('init change', function() {
                        editor.save();
                    });
                    editor.on('change', function() {
                        document.querySelector(`#${editor.id}`).value = editor.getContent();
                    });
                },
                images_upload_handler: function(blobInfo, success, failure) {
                    var formData = new FormData();
                    formData.append('file', blobInfo.blob(), blobInfo.filename());
                    formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute(
                        'content'));

                    fetch('/upload-editor-image', {
                            method: 'POST',
                            body: formData
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.url) {
                                success(data.url);
                            } else {
                                failure('خطأ في رفع الصورة: ' + (data.error || 'خطأ غير معروف'));
                            }
                        })
                        .catch(error => {
                            failure('خطأ في رفع الصورة');
                        });
                },
                file_picker_callback: function(callback, value, meta) {
                    if (meta.filetype === 'image') {
                        var input = document.createElement('input');
                        input.setAttribute('type', 'file');
                        input.setAttribute('accept', 'image/*');

                        input.onchange = function() {
                            var file = this.files[0];
                            var formData = new FormData();
                            formData.append('file', file);
                            formData.append('_token', document.querySelector('meta[name="csrf-token"]')
                                .getAttribute('content'));

                            fetch('/upload-editor-image', {
                                    method: 'POST',
                                    body: formData
                                })
                                .then(response => response.json())
                                .then(data => {
                                    if (data.url) {
                                        callback(data.url, {
                                            title: file.name
                                        });
                                    } else {
                                        alert('خطأ في رفع الصورة: ' + (data.error || 'خطأ غير معروف'));
                                    }
                                })
                                .catch(error => {
                                    alert('خطأ في رفع الصورة');
                                });
                        };
                        input.click();
                    }
                },
                //content_css: 'css/content.css',
                content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:14px }'
            });

            // تعيين البيانات إلى المحرر عند تحميل الصفحة
            tinymce.get('content_class').setContent(`{!! $content['class'] !!}`);
            tinymce.get('content_teacher').setContent(`{!! $content['teacher'] !!}`);
            tinymce.get('content_student').setContent(`{!! $content['student'] !!}`);
        }
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // تحديث معالج تحميل الملفات
            document.querySelectorAll('input[type="file"]').forEach(input => {
                input.addEventListener('change', function(event) {
                    handleFileUpload(event);
                });
            });
        });

        function handleFileUpload(event) {
            let fileInput = event.target;
            let file = fileInput.files[0];
            if (!file) return;

            let progressBarContainer = fileInput.closest('.modal-body').querySelector('.progress');
            let progressElement = progressBarContainer.querySelector('.progress-bar');

            // إظهار شريط التقدم
            progressBarContainer.style.display = 'block';
            progressElement.style.width = '0%';
            progressElement.textContent = '0%';

            let formData = new FormData();
            formData.append('file', file);
            formData.append('media_type', fileInput.getAttribute('data-media-type'));
            formData.append('content_id', document.querySelector('input[name="id"]').value);
            formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));

            // إرسال الطلب
            fetch('/upload', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // تحديث العرض
                        updateMediaPreview(fileInput.getAttribute('data-media-type'), data.media);
                        // إخفاء شريط التقدم
                        progressBarContainer.style.display = 'none';

                        // عرض رسالة نجاح
                        Swal.fire({
                            title: 'نجاح!',
                            text: 'تم تحميل الملف بنجاح',
                            icon: 'success',
                            confirmButtonText: 'حسناً'
                        }).then(() => {
                            // تحديث الصفحة لعرض الميديا الجديدة
                            location.reload();
                        });
                    } else {
                        showError(data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showError('حدث خطأ أثناء تحميل الملف');
                });
        }

        function updateMediaPreview(mediaType, mediaData) {
            const previewId = mediaType + 'Preview';
            const previewElement = document.getElementById(previewId);

            // تحديد نوع التاب من نوع الوسائط
            const tabType = mediaType.replace('_media', '');
            const videoCheckIcon = document.getElementById(tabType + 'VideoCheckIcon');

            if (previewElement) {
                if (mediaData.mime_type.startsWith('image/')) {
                    previewElement.innerHTML = `
                        <img src="${mediaData.url}" alt="Media Preview" class="w-100" />
                    `;
                    if (videoCheckIcon) {
                        videoCheckIcon.style.display = 'none';
                    }
                } else if (mediaData.mime_type.startsWith('video/')) {
                    previewElement.innerHTML = `
                        <video controls width="100%">
                            <source src="${mediaData.url}" type="${mediaData.mime_type}">
                            Your browser does not support the video tag.
                        </video>
                    `;
                    if (videoCheckIcon) {
                        videoCheckIcon.style.display = 'block';
                    }
                }
            }
        }

        function showError(message) {
            Swal.fire({
                title: 'خطأ!',
                text: message,
                icon: 'error',
                confirmButtonText: 'حسناً'
            });
        }

        // دالة لفحص وجود ميديا
        // function checkMediaExists(contentId, mediaType) {
        //     fetch('/check-media', {
        //             method: 'POST',
        //             headers: {
        //                 'Content-Type': 'application/json',
        //                 'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        //             },
        //             body: JSON.stringify({
        //                 content_id: contentId,
        //                 media_type: mediaType
        //             })
        //         })
        //         .then(response => response.json())
        //         .then(data => {
        //             if (data.success) {
        //                 const addButton = document.querySelector(`[data-media-type="${mediaType}"] .add_more_media`);
        //                 if (addButton) {
        //                     addButton.style.display = data.has_media ? 'block' : 'none';
        //                 }
        //             }
        //         })
        //         .catch(error => console.error('Error checking media:', error));
        // }
    </script>


    @push('scripts')
        <script>
            $(document).ready(function() {
                $.ajaxSetup({
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                });

                // زر إضافة سؤال يدوي
                $(document).on('click', '#addManualQuestionBtn', function(e) {
                    e.preventDefault();
                    $('#addManualQuestionModal').modal('show');
                });

                // أزرار تعديل الأسئلة المحفوظة
                $(document).on('click', '.edit-saved-question-btn', function(e) {
                    e.preventDefault();
                    const questionId = $(this).data('question-id');
                    const questionText = $(this).data('question-text');
                    const answerText = $(this).data('answer-text');
                    const difficulty = $(this).data('difficulty');
                    const type = $(this).data('type');

                    showEditModal(questionId, questionText, answerText, difficulty, type);
                });

                // أزرار حذف الأسئلة المحفوظة
                $(document).on('click', '.delete-saved-question-btn', function(e) {
                    e.preventDefault();
                    const questionId = $(this).data('question-id');
                    deleteSavedQuestion(questionId);
                });

                // Handle form submission
                $('#questionForm').on('submit', function(e) {
                    e.preventDefault();
                    const formData = $(this).serialize();
                    const isEdit = $('#question_id').val() !== '';
                    const url = isEdit ? '{{ route('teacher.update.question') }}' :
                        '{{ route('teacher.add.question') }}';

                    // Show loading state
                    const submitBtn = $(this).find('button[type="submit"]');
                    const originalBtnText = submitBtn.html();
                    submitBtn.prop('disabled', true).html(
                        '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> {{ __('Saving...') }}'
                    );

                    $.ajax({
                        url: url,
                        method: 'POST',
                        data: formData,
                        success: function(response) {
                            if (response.success) {
                                showToast('{{ __('Success') }}',
                                    '{{ __('Question saved successfully') }}',
                                    'success');
                                questionModal.hide();
                                // Instead of reloading, update the UI directly
                                if (isEdit) {
                                    // Update the existing question in the table
                                    const row = $(`tr[data-id="${response.data.id}"]`);
                                    if (row.length) {
                                        row.find('.question-text').text(response.data
                                            .question);
                                        row.find('.answer-text').text(response.data
                                            .answer);
                                        row.find('.badge')
                                            .removeClass(
                                                'bg-danger bg-warning bg-success')
                                            .addClass('bg-' + (response.data
                                                .difficulty === 'صعب' ? 'danger' : (
                                                    response.data.difficulty ===
                                                    'متوسط' ? 'warning' : 'success')
                                            ))
                                            .text(response.data.difficulty);
                                        // Update data attributes
                                        row.find('.edit-question')
                                            .data('question', response.data.question)
                                            .data('answer', response.data.answer)
                                            .data('difficulty', response.data
                                                .difficulty);
                                    }
                                } else {
                                    // Add new question to the table
                                    const newRow = `
                                        <tr class="question-item" data-id="${response.data.id}">
                                            <td style="width: 70%;">
                                                <div class="question-text fw-bold mb-2">${$('tbody tr').length + 1}. ${response.data.question}</div>
                                                <div class="answer-text text-muted">${response.data.answer}</div>
                                                <div class="mt-2">
                                                    <span class="badge bg-${response.data.difficulty === 'صعب' ? 'danger' : (response.data.difficulty === 'متوسط' ? 'warning' : 'success')}">
                                                        ${response.data.difficulty}
                                                    </span>
                                                </div>
                                            </td>
                                            <td class="text-end align-middle">
                                                <button type="button" class="btn btn-sm btn-outline-primary edit-question"
                                                    data-id="${response.data.id}"
                                                    data-question="${response.data.question}"
                                                    data-answer="${response.data.answer}"
                                                    data-difficulty="${response.data.difficulty}">
                                                    <i class="fas fa-edit"></i> {{ __('Edit') }}
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-danger delete-question"
                                                    data-id="${response.data.id}">
                                                    <i class="fas fa-trash"></i> {{ __('Delete') }}
                                                </button>
                                            </td>
                                        </tr>
                                    `;
                                    $('tbody').append(newRow);

                                    // Update the "no questions" message if it exists
                                    if ($('.no-questions-message').length) {
                                        $('.no-questions-message').remove();
                                    }
                                }

                                // Reset and close the form
                                $('#questionForm')[0].reset();
                                questionModal.hide();
                            } else {
                                showToast('{{ __('Error') }}', response.message ||
                                    '{{ __('An error occurred') }}', 'error');
                            }
                        },
                        error: function(xhr) {
                            const errorMsg = xhr.responseJSON?.message ||
                                '{{ __('An error occurred while saving the question') }}';
                            showToast('{{ __('Error') }}', errorMsg, 'error');
                        },
                        complete: function() {
                            submitBtn.prop('disabled', false).html(originalBtnText);
                        }
                    });
                });

                // Handle delete confirmation
                $('#confirmDelete').on('click', function() {
                    if (!currentQuestionId) return;

                    const deleteBtn = $(this);
                    const originalBtnText = deleteBtn.html();
                    deleteBtn.prop('disabled', true).html(
                        '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> {{ __('Deleting...') }}'
                    );

                    $.ajax({
                        url: '{{ route('teacher.delete.question') }}',
                        method: 'POST',
                        data: {
                            _token: '{{ csrf_token() }}',
                            question_id: currentQuestionId
                        },
                        success: function(response) {
                            if (response.success) {
                                showToast('{{ __('Success') }}',
                                    '{{ __('Question deleted successfully') }}',
                                    'success');
                                // Remove the deleted question row
                                $(`tr[data-id="${currentQuestionId}"]`).fadeOut(300,
                                    function() {
                                        $(this).remove();

                                        // Show "no questions" message if this was the last question
                                        if ($('tbody tr').length === 0) {
                                            $('tbody').html(`
                                            <tr class="no-questions-message">
                                                <td colspan="2" class="text-center py-4">
                                                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                                    <p class="text-muted">{{ __('No questions added yet. Click the button above to add one.') }}</p>
                                                </td>
                                            </tr>
                                        `);
                                        } else {
                                            // Update question numbers
                                            $('tbody tr').each(function(index) {
                                                $(this).find(
                                                        '.question-text')
                                                    .text(
                                                        `${index + 1}. ${$(this).find('.question-text').text().substring(3)}`
                                                    );
                                            });
                                        }
                                    });
                            } else {
                                showToast('{{ __('Error') }}', response.message ||
                                    '{{ __('An error occurred') }}', 'error');
                            }
                        },
                        error: function(xhr) {
                            const errorMsg = xhr.responseJSON?.message ||
                                '{{ __('An error occurred while deleting the question') }}';
                            showToast('{{ __('Error') }}', errorMsg, 'error');
                        },
                        complete: function() {
                            deleteModal.hide();
                            deleteBtn.prop('disabled', false).html(originalBtnText);
                            currentQuestionId = null;
                        }
                    });
                });

                // Show toast notification
                function showToast(title, message, type = 'info') {
                    const Toast = Swal.mixin({
                        toast: true,
                        position: 'top-end',
                        showConfirmButton: false,
                        timer: 3000,
                        timerProgressBar: true,
                        didOpen: (toast) => {
                            toast.addEventListener('mouseenter', Swal.stopTimer);
                            toast.addEventListener('mouseleave', Swal.resumeTimer);
                        }
                    });

                    Toast.fire({
                        icon: type,
                        title: title,
                        text: message
                    });
                }
            });
        </script>

        {{-- <script>
            function deleteMedia(event, url, id, mediaType) {
                event.preventDefault(); // منع السلوك الافتراضي للرابط

                Swal.fire({
                    title: 'هل أنت متأكد؟',
                    text: 'هل تريد حقاً حذف هذا الملف؟',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'نعم، احذف',
                    cancelButtonText: 'إلغاء'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // تحقق من أن المعرف والنوع ليسا فارغين
                        if (id && mediaType) {
                            console.log("Sending delete request for media ID:", id, "and type:", mediaType);
                            // أرسل طلب الحذف باستخدام Ajax
                            $.ajax({
                                url: url, // استخدم URL الممرر للدالة
                                method: 'POST',
                                data: {
                                    id: id,
                                    media_type: mediaType,
                                    _token: '{{ csrf_token() }}' // تأكد من إضافة التوكن لحماية CSRF
                                },
                                success: function(response) {
                                    if (response.success) {
                                        Swal.fire('تم الحذف!', response.message, 'success');
                                        console.log("Media deleted successfully:", response);
                                        // قم بإجراء تحديث في الواجهة أو إزالة العنصر المحذوف
                                        location.reload();
                                    } else {
                                        Swal.fire('خطأ!', response.message, 'error');
                                        console.log("Failed to delete media:", response);
                                    }
                                },
                                error: function(xhr, status, error) {
                                    console.error('Error occurred:', error);
                                    Swal.fire('خطأ!', 'حدث خطأ أثناء حذف الملف', 'error');
                                }
                            });
                        } else {
                            Swal.fire('خطأ!', 'معرف الميديا أو النوع غير صالح', 'error');
                            console.log("Invalid media id or type:", id, mediaType);
                        }
                    }
                });
            }
        </script> --}}
        <script>
            function triggerFileInput(inputId) {
                document.getElementById(inputId).click();
            }

            function deleteMedia(event, url, id, mediaType, mediaId) {
                event.preventDefault(); // منع السلوك الافتراضي للرابط

                Swal.fire({
                    title: 'هل أنت متأكد؟',
                    text: 'هل تريد حقاً حذف هذا الملف؟',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'نعم، احذف',
                    cancelButtonText: 'إلغاء'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // تحقق من أن المعرف والنوع ليسا فارغين
                        if (id && mediaType && mediaId) {
                            console.log("Sending delete request for media ID:", mediaId, "and type:", mediaType);
                            // أرسل طلب الحذف باستخدام Ajax
                            $.ajax({
                                url: url, // استخدم URL الممرر للدالة
                                method: 'POST',
                                data: {
                                    id: id,
                                    media_type: mediaType,
                                    media_id: mediaId,
                                    _token: '{{ csrf_token() }}' // تأكد من إضافة التوكن لحماية CSRF
                                },
                                success: function(response) {
                                    if (response.success) {
                                        Swal.fire('تم الحذف!', response.message, 'success');
                                        console.log("Media deleted successfully:", response);
                                        // قم بإجراء تحديث في الواجهة أو إزالة العنصر المحذوف
                                        location.reload();
                                    } else {
                                        Swal.fire('خطأ!', response.message, 'error');
                                        console.log("Failed to delete media:", response);
                                    }
                                },
                                error: function(xhr, status, error) {
                                    console.error('Error occurred:', error);
                                    Swal.fire('خطأ!', 'حدث خطأ أثناء حذف الملف', 'error');
                                }
                            });
                        } else {
                            Swal.fire('خطأ!', 'معرف الميديا أو النوع غير صالح', 'error');
                            console.log("Invalid media id or type:", id, mediaType, mediaId);
                        }
                    }
                });
            }

            // تعيين معرف المحتوى الحالي كمتغير عام
            window.currentContentId = {{ $data->id }};

            // فحص وجود ميديا عند تحميل الصفحة
            document.addEventListener('DOMContentLoaded', function() {
            // checkMediaExists({{ $data->id }}, 'class_media');
            // checkMediaExists({{ $data->id }}, 'teacher_media');
            // checkMediaExists({{ $data->id }}, 'student_media');











            function performSaveNewQuestion(index) {
                const card = document.querySelector(`#suggestedQuestionsSection [data-question-index="${index}"]`);
                if (!card) return;

                const saveBtn = card.querySelector('.save-question-btn');
                const question = saveBtn.getAttribute('data-question');
                const answer = saveBtn.getAttribute('data-answer');
                const difficulty = saveBtn.getAttribute('data-difficulty');
                const type = saveBtn.getAttribute('data-type');
                const contentId = window.currentContentId;

                if (!contentId) {
                    Swal.fire('خطأ', 'لم يتم العثور على معرف المحتوى', 'error');
                    return;
                }

                // تغيير حالة الزر
                const originalContent = saveBtn.innerHTML;
                saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
                saveBtn.disabled = true;

                // إرسال طلب الحفظ
                $.ajax({
                    url: '/ai-questions/save',
                    method: 'POST',
                    data: {
                        content_id: contentId,
                        question: question,
                        answer: answer,
                        difficulty: difficulty,
                        type: type,
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        if (response.success) {
                            saveBtn.innerHTML = '<i class="fas fa-check"></i> تم الحفظ';
                            saveBtn.classList.remove('btn-success');
                            saveBtn.classList.add('btn-secondary');
                            saveBtn.disabled = true;

                            // إضافة السؤال إلى قائمة الأسئلة المحفوظة
                            addToSavedQuestions(response.question);

                            Swal.fire('تم الحفظ بنجاح!',
                                'تم حفظ السؤال في قاعدة البيانات وإضافته للأسئلة المحفوظة',
                                'success');
                        } else {
                            saveBtn.innerHTML = originalContent;
                            saveBtn.disabled = false;
                            Swal.fire('فشل في الحفظ', response.message ||
                                'حدث خطأ أثناء حفظ السؤال. يرجى المحاولة مرة أخرى.', 'error');
                        }
                    },
                    error: function(xhr) {
                        console.error('Error:', xhr);
                        saveBtn.innerHTML = originalContent;
                        saveBtn.disabled = false;
                        Swal.fire('خطأ في الاتصال',
                            'حدث خطأ في الاتصال بالخادم. يرجى التحقق من الاتصال والمحاولة مرة أخرى.',
                            'error');
                    }
                });
            }

            // إضافة السؤال إلى قائمة الأسئلة المحفوظة
            function addToSavedQuestions(question) {
                console.log('➕ addToSavedQuestions called with:', question);

                const savedContainer = document.getElementById('savedQuestionsContainer');
                const noQuestionsMessage = document.getElementById('noQuestionsMessage');
                const questionsCount = document.getElementById('questionsCount');

                console.log('🔍 Container elements found:', {
                    savedContainer: savedContainer,
                    noQuestionsMessage: noQuestionsMessage,
                    questionsCount: questionsCount
                });

                // إخفاء رسالة "لا توجد أسئلة" إذا كانت موجودة
                if (noQuestionsMessage) {
                    noQuestionsMessage.style.display = 'none';
                }

                // إنشاء قائمة الأسئلة إذا لم تكن موجودة
                let questionsList = document.getElementById('savedQuestionsList');
                if (!questionsList) {
                    questionsList = document.createElement('div');
                    questionsList.id = 'savedQuestionsList';
                    savedContainer.appendChild(questionsList);
                }

                // إضافة السؤال الجديد
                const questionIndex = questionsList.children.length + 1;
                const difficultyClass = question.difficulty == 'صعب' ? 'danger' : (question.difficulty == 'متوسط' ?
                    'warning' : 'success');
                const questionCard = `
                        <div class="card mb-3 saved-question-card" data-question-id="${question.id}">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="fw-bold text-primary mb-2">س${questionIndex}: <span class="question-text">${question.question}</span></div>
                                        <div class="text-muted mb-2">ج: <span class="answer-text">${question.answer}</span></div>
                                        <div>
                                            <span class="badge bg-${difficultyClass} difficulty-badge">${question.difficulty}</span>
                                            <span class="badge bg-info ms-1 type-badge">${question.type || 'عام'}</span>
                                            <span class="badge bg-secondary ms-1">${question.is_ai_generated ? 'ذكاء اصطناعي' : 'يدوي'}</span>
                                            ${question.status ?
                                                '<span class="badge bg-success ms-1 status-badge">مفعل</span>' :
                                                '<span class="badge bg-warning ms-1 status-badge">غير مفعل</span>'
                                            }
                                            ${question.user_id && question.user_id != {{ auth()->id() }} ?
                                                '<span class="badge bg-primary ms-1">' + (question.user_name || 'معلم') + '</span>' :
                                                ''
                                            }
                                        </div>
                                    </div>
                                    <div class="col-md-4 text-end">
                                        <div class="btn-group-vertical" role="group">
                                            <a href="javascript:void(0)" class="btn btn-sm btn-outline-primary mb-1 edit-saved-question-btn"
                                                data-question-id="${question.id}"
                                                data-question-text="${question.question}"
                                                data-answer-text="${question.answer}"
                                                data-difficulty="${question.difficulty}"
                                                data-type="${question.type || 'عام'}">
                                                <i class="fas fa-edit"></i> تعديل
                                            </a>
                                            ` + ({{ auth()->user()->type }} === 1 && question.user_id && question.user_id != {{ auth()->id() }} ?
                                                (question.status ?
                                                    '<button type="button" class="btn btn-sm btn-outline-warning mb-1 toggle-question-status-btn" data-question-id="' + question.id + '" data-current-status="1"><i class="fas fa-times"></i> إلغاء التفعيل</button>' :
                                                    '<button type="button" class="btn btn-sm btn-outline-success mb-1 toggle-question-status-btn" data-question-id="' + question.id + '" data-current-status="0"><i class="fas fa-check"></i> تفعيل السؤال</button>'
                                                ) : ''
                                            ) + `
                                            <a href="javascript:void(0)" class="btn btn-sm btn-outline-danger delete-saved-question-btn" data-question-id="${question.id}">
                                                <i class="fas fa-trash"></i> حذف
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;

                questionsList.insertAdjacentHTML('beforeend', questionCard);

                // تحديث عداد الأسئلة
                const currentCount = parseInt(questionsCount.textContent.match(/\d+/)[0]) + 1;
                questionsCount.textContent = `${currentCount} سؤال`;

                // ربط الأحداث للسؤال الجديد
                bindSavedQuestionEvents();
            }

            // دوال التعامل مع الأسئلة المحفوظة

            function deleteSavedQuestion(questionId) {
                console.log('deleteSavedQuestion called with ID:', questionId);

                if (!questionId) {
                    console.error('No question ID provided');
                    Swal.fire('خطأ', 'لم يتم تحديد السؤال للحذف', 'error');
                    return;
                }

                Swal.fire({
                    title: 'تأكيد الحذف',
                    text: 'هل أنت متأكد من حذف هذا السؤال نهائياً من قاعدة البيانات؟',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'نعم، احذف نهائياً',
                    cancelButtonText: 'إلغاء',
                    confirmButtonColor: '#dc3545'
                }).then((result) => {
                    if (result.isConfirmed) {
                        console.log('Delete confirmed for question:', questionId);
                        performDeleteSavedQuestion(questionId);
                    }
                });
            }

            function performDeleteSavedQuestion(questionId) {
                $.ajax({
                    url: '/ai-questions/delete',
                    method: 'POST',
                    data: {
                        question_id: questionId,
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        if (response.success) {
                            // حذف الكارت من العرض
                            const card = document.querySelector(`[data-question-id="${questionId}"]`);
                            if (card) {
                                card.remove();
                            }

                            // تحديث عداد الأسئلة
                            const questionsCount = document.getElementById('questionsCount');
                            const currentCount = parseInt(questionsCount.textContent.match(/\d+/)[0]) -
                                1;
                            questionsCount.textContent = `${currentCount} سؤال`;

                            // إظهار رسالة "لا توجد أسئلة" إذا لم تعد هناك أسئلة
                            const questionsList = document.getElementById('savedQuestionsList');
                            if (questionsList && questionsList.children.length === 0) {
                                const noQuestionsMessage = document.getElementById(
                                    'noQuestionsMessage');
                                if (noQuestionsMessage) {
                                    noQuestionsMessage.style.display = 'block';
                                }
                            }

                            Swal.fire('تم الحذف بنجاح!', 'تم حذف السؤال نهائياً من قاعدة البيانات',
                                'success');
                        } else {
                            Swal.fire('فشل في الحذف', response.message ||
                                'حدث خطأ أثناء حذف السؤال. يرجى المحاولة مرة أخرى.', 'error');
                        }
                    },
                    error: function(xhr) {
                        console.error('Error:', xhr);
                        Swal.fire('خطأ', 'حدث خطأ في الاتصال', 'error');
                    }
                });
            }

            // دالة عرض مودل التعديل
            function showEditModal(id, questionText, answerText, difficulty, type, questionType) {
                try {
                    console.log('showEditModal called with:', {
                        id,
                        questionText,
                        answerText,
                        difficulty,
                        type,
                        questionType
                    });

                    // التحقق من وجود Bootstrap
                    if (typeof bootstrap === 'undefined') {
                        console.error('❌ Bootstrap not available for modal');
                        alert('خطأ: لا يمكن فتح نافذة التعديل. يرجى تحديث الصفحة.');
                        return;
                    }

                    // إزالة جميع المودالات الموجودة (ما عدا المودالات الأساسية)
                    document.querySelectorAll(
                        '.modal:not(#questionModal):not(#deleteModal):not(#addManualQuestionModal)').forEach(
                        modal => {
                            const modalInstance = bootstrap.Modal.getInstance(modal);
                            if (modalInstance) {
                                modalInstance.hide();
                            }
                            modal.remove();
                        });

                    // إزالة جميع backdrop
                    document.querySelectorAll('.modal-backdrop').forEach(backdrop => backdrop.remove());

                    // إعادة تعيين body
                    document.body.classList.remove('modal-open');
                    document.body.style.overflow = '';
                    document.body.style.paddingRight = '';

                    const modalId = `editModal_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

                    const modalHtml = `
                        <div class="modal fade" id="${modalId}" tabindex="-1" style="z-index: 1060;">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title">تعديل السؤال</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                    </div>
                                    <div class="modal-body">
                                        <div class="mb-3">
                                            <label class="form-label">السؤال:</label>
                                            <textarea class="form-control edit-question" rows="3">${questionText}</textarea>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">الإجابة:</label>
                                            <div class="input-group">
                                                <textarea class="form-control edit-answer" rows="4">${answerText}</textarea>
                                                <button type="button" class="btn btn-outline-info regenerate-answer-btn" data-question-id="${id}" data-question-type="${questionType}">
                                                    <i class="fas fa-robot"></i> إجابة بالذكاء الاصطناعي
                                                </button>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <label class="form-label">الصعوبة:</label>
                                                <select class="form-select edit-difficulty">
                                                    <option value="سهل" ${difficulty === "سهل" ? "selected" : ""}>سهل</option>
                                                    <option value="متوسط" ${difficulty === "متوسط" ? "selected" : ""}>متوسط</option>
                                                    <option value="صعب" ${difficulty === "صعب" ? "selected" : ""}>صعب</option>
                                                </select>
                                            </div>
                                            <div class="col-md-6">
                                                <label class="form-label">النوع:</label>
                                                <input type="text" class="form-control edit-type" value="${type}">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                        <button type="button" class="btn btn-success save-edit-btn" data-question-id="${id}" data-question-type="${questionType}">
                                            <i class="fas fa-save"></i> حفظ التعديل
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;

                    // إضافة المودل للصفحة
                    document.body.insertAdjacentHTML("beforeend", modalHtml);

                    const modalElement = document.getElementById(modalId);

                    // ربط أحداث الأزرار
                    const regenerateBtn = modalElement.querySelector('.regenerate-answer-btn');
                    if (regenerateBtn) {
                        regenerateBtn.addEventListener('click', function() {
                            const questionText = modalElement.querySelector('.edit-question').value.trim();
                            if (!questionText) {
                                Swal.fire('سؤال مطلوب', 'يرجى كتابة السؤال أولاً', 'warning');
                                return;
                            }

                            const originalText = this.innerHTML;
                            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التوليد...';
                            this.disabled = true;

                            fetch('{{ route('generate.ai.answer') }}', {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/json',
                                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                                    },
                                    body: JSON.stringify({
                                        question: questionText,
                                        content_id: {{ $data->id }}
                                    })
                                })
                                .then(r => r.json())
                                .then(data => {
                                    if (data.success) {
                                        modalElement.querySelector('.edit-answer').value = data.answer;
                                        Swal.fire('تم!', 'تم توليد الإجابة بنجاح', 'success');
                                    } else {
                                        Swal.fire('خطأ', data.message || 'فشل في توليد الإجابة',
                                            'error');
                                    }
                                })
                                .catch(() => {
                                    Swal.fire('خطأ', 'حدث خطأ في الاتصال', 'error');
                                })
                                .finally(() => {
                                    this.innerHTML = originalText;
                                    this.disabled = false;
                                });
                        });
                    }

                    modalElement.querySelector('.save-edit-btn').addEventListener('click', function() {
                        confirmSaveEdit(this.dataset.questionId, this.dataset.questionType, modalId);
                    });

                    // منع تداخل الأحداث مع inputs
                    modalElement.querySelectorAll('input, textarea, select').forEach(input => {
                        input.addEventListener('click', function(e) {
                            e.stopPropagation();
                        });
                        input.addEventListener('focus', function(e) {
                            e.stopPropagation();
                        });
                    });

                    // عرض المودل
                    const modal = new bootstrap.Modal(modalElement);
                    modal.show();

                    // حذف المودل عند إغلاقه
                    modalElement.addEventListener("hidden.bs.modal", function() {
                        this.remove();
                        // تنظيف أي backdrop متبقي
                        document.querySelectorAll('.modal-backdrop').forEach(backdrop => backdrop.remove());
                        document.body.classList.remove('modal-open');
                        document.body.style.overflow = '';
                        document.body.style.paddingRight = '';
                    });
                } catch (error) {
                    console.error('❌ Error in showEditModal:', error);
                    alert('حدث خطأ في فتح نافذة التعديل. يرجى تحديث الصفحة والمحاولة مرة أخرى.');
                }
            }

            // تأكيد حفظ التعديل
            function confirmSaveEdit(id, questionType, modalId) {
                const modal = document.getElementById(modalId);
                const newQuestion = modal.querySelector(".edit-question").value.trim();
                const newAnswer = modal.querySelector(".edit-answer").value.trim();
                const newDifficulty = modal.querySelector(".edit-difficulty").value;
                const newType = modal.querySelector(".edit-type").value.trim();

                if (!newQuestion || !newAnswer) {
                    Swal.fire("حقول مطلوبة", "يرجى ملء السؤال والإجابة قبل الحفظ", "warning");
                    return;
                }

                // تأكيد الحفظ
                Swal.fire({
                    title: "تأكيد التعديل",
                    text: "هل أنت متأكد من حفظ التعديلات؟",
                    icon: "question",
                    showCancelButton: true,
                    confirmButtonText: "نعم، احفظ",
                    cancelButtonText: "إلغاء",
                    confirmButtonColor: "#28a745"
                }).then((result) => {
                    if (result.isConfirmed) {
                        if (questionType === 'saved') {
                            updateSavedQuestion(id, newQuestion, newAnswer, newDifficulty, newType);
                        } else if (questionType === 'suggested') {
                            // تحديث السؤال المقترح في العرض فقط (بدون حفظ في قاعدة البيانات)
                            updateSuggestedQuestion(id, newQuestion, newAnswer, newDifficulty, newType);
                        }

                        // إغلاق المودل
                        bootstrap.Modal.getInstance(modal).hide();
                    }
                });
            }

            // تحديث السؤال المحفوظ
            function updateSavedQuestion(questionId, question, answer, difficulty, type) {
                $.ajax({
                    url: '/ai-questions/update',
                    method: 'POST',
                    data: {
                        question_id: questionId,
                        question: question,
                        answer: answer,
                        difficulty: difficulty,
                        type: type,
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        if (response.success) {
                            // تحديث الكارت في العرض
                            updateSavedQuestionCard(questionId, question, answer, difficulty, type);
                            Swal.fire("تم التحديث بنجاح!",
                                "تم تحديث السؤال وحفظ التغييرات في قاعدة البيانات", "success");
                        } else {
                            Swal.fire('فشل في التحديث', response.message ||
                                'حدث خطأ أثناء تحديث السؤال. يرجى المحاولة مرة أخرى.', 'error');
                        }
                    },
                    error: function(xhr) {
                        console.error('Error:', xhr);
                        Swal.fire('خطأ', 'حدث خطأ في الاتصال', 'error');
                    }
                });
            }

            // تحديث عداد الأسئلة المقترحة
            function updateSuggestedQuestionsCount() {
                const suggestedContainer = document.getElementById('suggestedQuestionsContainer');
                const suggestedCount = document.getElementById('suggestedQuestionsCount');
                const questionCards = suggestedContainer.querySelectorAll('.ai-question-card');
                suggestedCount.textContent = `${questionCards.length} سؤال`;

                // إخفاء القسم إذا لم تعد هناك أسئلة
                if (questionCards.length === 0) {
                    document.getElementById('suggestedQuestionsSection').style.display = 'none';
                }
            }

            // تحديث السؤال المقترح
            function updateSuggestedQuestion(index, question, answer, difficulty, type) {
                const questionIndex = parseInt(index.replace('suggested_', ''));
                const li = document.querySelector(`#suggestedQuestionsSection li:nth-child(${questionIndex + 1})`);
                if (!li) {
                    console.error('Could not find suggested question element for index:', index);
                    return;
                }

                // تحديث النص في العنصر
                const questionDiv = li.querySelector('.fw-bold');
                const answerDiv = li.querySelector('.text-muted');
                const badges = li.querySelectorAll('.badge');

                if (questionDiv) questionDiv.innerHTML = 'س' + (questionIndex + 1) + ': ' + question;
                if (answerDiv) answerDiv.innerHTML = 'ج: ' + answer;

                // تحديث شارة الصعوبة
                if (badges[0]) {
                    badges[0].textContent = difficulty;
                    badges[0].className =
                        `badge bg-${difficulty == 'صعب' ? 'danger' : (difficulty == 'متوسط' ? 'warning' : 'success')}`;
                }

                // تحديث شارة النوع
                if (badges[1]) {
                    badges[1].textContent = type;
                }

                // تحديث البيانات في العنصر
                li.setAttribute('data-question', question);
                li.setAttribute('data-answer', answer);
                li.setAttribute('data-difficulty', difficulty);
                li.setAttribute('data-type', type);

                // تحديث البيانات في الأزرار
                const buttons = li.querySelectorAll('button');
                buttons.forEach(btn => {
                    if (btn.classList.contains('save-suggested-question-btn') || btn.classList.contains(
                            'edit-suggested-question-btn')) {
                        btn.setAttribute('data-question', question);
                        btn.setAttribute('data-answer', answer);
                        btn.setAttribute('data-difficulty', difficulty);
                        btn.setAttribute('data-type', type);
                    }
                });

                Swal.fire('تم التحديث!', 'تم تحديث السؤال المقترح بنجاح', 'success');
            }

            // دالة حفظ السؤال اليدوي
            function saveManualQuestion() {
                console.log('💾 saveManualQuestion called');

                // فحص العناصر
                const questionElement = document.getElementById('manualQuestion');
                const answerElement = document.getElementById('manualAnswer');
                const difficultyElement = document.getElementById('manualDifficulty');
                const typeElement = document.getElementById('manualType');

                console.log('🔍 Form elements found:', {
                    questionElement: questionElement,
                    answerElement: answerElement,
                    difficultyElement: difficultyElement,
                    typeElement: typeElement
                });

                if (!questionElement || !answerElement || !difficultyElement || !typeElement) {
                    console.error('❌ Some form elements not found!');
                    alert('خطأ: لم يتم العثور على عناصر النموذج');
                    return;
                }

                const question = questionElement.value.trim();
                const answer = answerElement.value.trim();
                const difficulty = difficultyElement.value;
                const type = typeElement.value.trim();

                console.log('📋 Manual question data:', {
                    question,
                    answer,
                    difficulty,
                    type
                });

                if (!question || !answer) {
                    console.warn('⚠️ Missing required fields');
                    Swal.fire("حقول مطلوبة", "يرجى ملء السؤال والإجابة", "warning");
                    return;
                }

                console.log('📡 Sending AJAX request to save question...');

                // حفظ السؤال في قاعدة البيانات
                $.ajax({
                    url: '{{ route('teacher.add.question') }}',
                    method: 'POST',
                    data: {
                        content_id: {{ $data->id }},
                        question: question,
                        answer: answer,
                        difficulty: difficulty,
                        type: type,
                        is_ai_generated: false,
                        _token: '{{ csrf_token() }}'
                    },
                    beforeSend: function() {
                        console.log('⏳ AJAX request started...');
                    },
                    success: function(response) {
                        console.log('✅ AJAX success response:', response);

                        if (response.success) {
                            console.log('✅ Question saved successfully:', response.question);

                            // إغلاق المودال
                            const modalElement = document.getElementById('addManualQuestionModal');
                            if (modalElement) {
                                console.log('🔒 Closing modal...');
                                bootstrap.Modal.getInstance(modalElement).hide();
                            }

                            // إضافة السؤال للعرض
                            console.log('➕ Adding question to saved questions display...');
                            addToSavedQuestions(response.question);

                            // تنظيف النموذج
                            console.log('🧹 Cleaning form...');
                            document.getElementById('manualQuestion').value = '';
                            document.getElementById('manualAnswer').value = '';
                            document.getElementById('manualDifficulty').value = 'متوسط';
                            document.getElementById('manualType').value = 'عام';

                            console.log('🎉 Showing success message...');
                            Swal.fire('تم الحفظ!', 'تم إضافة السؤال بنجاح', 'success');
                        } else {
                            console.error('❌ Server returned error:', response.message);
                            Swal.fire('خطأ', response.message || 'فشل في حفظ السؤال', 'error');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('❌ AJAX error:', {
                            xhr,
                            status,
                            error
                        });
                        console.error('❌ Response text:', xhr.responseText);
                        Swal.fire('خطأ', 'حدث خطأ في الاتصال', 'error');
                    }
                });
            }

            // دالة توليد إجابة بالذكاء الاصطناعي
            function generateAIAnswer() {
                console.log('generateAIAnswer called');

                const question = document.getElementById('manualQuestion').value.trim();
                console.log('Question for AI:', question);

                if (!question) {
                    Swal.fire("سؤال مطلوب", "يرجى كتابة السؤال أولاً", "warning");
                    return;
                }

                // إظهار مؤشر التحميل
                const btn = document.getElementById('aiAnswerBtn');
                const originalText = btn.innerHTML;
                btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التوليد...';
                btn.disabled = true;

                $.ajax({
                    url: '{{ route('generate.ai.answer') }}',
                    method: 'POST',
                    data: {
                        question: question,
                        content_id: {{ $data->id }},
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        if (response.success) {
                            document.getElementById('manualAnswer').value = response.answer;
                            Swal.fire('تم!', 'تم توليد الإجابة بنجاح', 'success');
                        } else {
                            Swal.fire('خطأ', response.message || 'فشل في توليد الإجابة', 'error');
                        }
                    },
                    error: function() {
                        Swal.fire('خطأ', 'حدث خطأ في الاتصال', 'error');
                    },
                    complete: function() {
                        btn.innerHTML = originalText;
                        btn.disabled = false;
                    }
                });
            }

            // دالة إعادة توليد الإجابة في مودال التعديل
            function regenerateAnswer(id, questionType, modalId) {
                const modal = document.getElementById(modalId);
                const questionText = modal.querySelector('.edit-question').value.trim();

                if (!questionText) {
                    Swal.fire("سؤال مطلوب", "يرجى كتابة السؤال أولاً", "warning");
                    return;
                }

                // إظهار مؤشر التحميل
                const btn = modal.querySelector('.regenerate-answer-btn');
                const originalText = btn.innerHTML;
                btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التوليد...';
                btn.disabled = true;

                $.ajax({
                    url: '{{ route('generate.ai.answer') }}',
                    method: 'POST',
                    data: {
                        question: questionText,
                        content_id: {{ $data->id }},
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        if (response.success) {
                            modal.querySelector('.edit-answer').value = response.answer;
                            Swal.fire('تم!', 'تم توليد الإجابة بنجاح', 'success');
                        } else {
                            Swal.fire('خطأ', response.message || 'فشل في توليد الإجابة', 'error');
                        }
                    },
                    error: function() {
                        Swal.fire('خطأ', 'حدث خطأ في الاتصال', 'error');
                    },
                    complete: function() {
                        btn.innerHTML = originalText;
                        btn.disabled = false;
                    }
                });
            }

            // تحديث كارت السؤال المحفوظ
            function updateSavedQuestionCard(questionId, question, answer, difficulty, type) {
                const card = document.querySelector(`[data-question-id="${questionId}"]`);
                if (!card) return;

                card.querySelector('.question-text').textContent = question;
                card.querySelector('.answer-text').textContent = answer;
                card.querySelector('.difficulty-badge').textContent = difficulty;
                card.querySelector('.type-badge').textContent = type;

                // تحديث لون شارة الصعوبة
                const difficultyBadge = card.querySelector('.difficulty-badge');
                difficultyBadge.className =
                    `badge difficulty-badge bg-${difficulty == 'صعب' ? 'danger' : (difficulty == 'متوسط' ? 'warning' : 'success')}`;
            }



            // ربط الأحداث للميزات الجديدة - تبسيط الكود
            setTimeout(function() {
                console.log('🔄 Starting to bind events for questions...');

                // ربط أحداث الأسئلة المحفوظة
                bindSavedQuestionEvents();

                // ربط أحداث الأسئلة المقترحة
                bindNewQuestionEvents();

                console.log('✅ Event binding completed');
            }, 500);

            // ربط الأحداث الأساسية باستخدام event delegation
            $(document).on('click', '#addManualQuestionBtn', function(e) {
                e.preventDefault();
                const modalElement = document.getElementById('addManualQuestionModal');
                if (modalElement) {
                    const modal = new bootstrap.Modal(modalElement);
                    modal.show();
                } else {
                    Swal.fire('خطأ', 'لم يتم العثور على نافذة إضافة السؤال', 'error');
                }
            });

            $(document).on('click', '#saveManualQuestionBtn', function(e) {
                e.preventDefault();
                saveManualQuestion();
            });

            $(document).on('click', '#aiAnswerBtn', function(e) {
                e.preventDefault();
                generateAIAnswer();
            });

            $(document).on('click', '#reAnalyzeBtn', function(e) {
                e.preventDefault();
                performFullReanalysis();
            });

            // ربط أحداث تفعيل/إلغاء تفعيل الأسئلة
            $(document).on('click', '.toggle-question-status-btn', function(e) {
                e.preventDefault();
                const questionId = $(this).data('question-id');
                const currentStatus = $(this).data('current-status');
                toggleQuestionStatus(questionId, currentStatus);
            });

            // دالة ربط أحداث الأسئلة المحفوظة
            function bindSavedQuestionEvents() {
                // ربط أحداث التعديل والحذف
                $('.edit-saved-question-btn').off('click').on('click', function(e) {
                    e.preventDefault();
                    const questionId = $(this).data('question-id');
                    const questionText = $(this).data('question-text');
                    const answerText = $(this).data('answer-text');
                    const difficulty = $(this).data('difficulty');
                    const type = $(this).data('type');

                    editSavedQuestion(questionId, questionText, answerText, difficulty, type);
                });

                $('.delete-saved-question-btn').off('click').on('click', function(e) {
                    e.preventDefault();
                    const questionId = $(this).data('question-id');
                    deleteSavedQuestion(questionId);
                });
            }

            // دالة تفعيل/إلغاء تفعيل السؤال
            function toggleQuestionStatus(questionId, currentStatus) {
                const newStatus = currentStatus == 1 ? 0 : 1;
                const action = newStatus == 1 ? 'تفعيل' : 'إلغاء تفعيل';

                Swal.fire({
                    title: `هل أنت متأكد من ${action} هذا السؤال؟`,
                    text: newStatus == 1 ? 'سيصبح السؤال مرئياً لجميع المعلمين' : 'سيصبح السؤال مرئياً للمعلم الذي أضافه فقط',
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonColor: newStatus == 1 ? '#28a745' : '#ffc107',
                    cancelButtonColor: '#6c757d',
                    confirmButtonText: `نعم، ${action}`,
                    cancelButtonText: 'إلغاء'
                }).then((result) => {
                    if (result.isConfirmed) {
                        const url = newStatus == 1 ? '{{ route("admin.questions.activate") }}' : '{{ route("admin.questions.deactivate") }}';

                        $.ajax({
                            url: url,
                            method: 'POST',
                            data: {
                                question_id: questionId,
                                _token: '{{ csrf_token() }}'
                            },
                            success: function(response) {
                                if (response.success) {
                                    Swal.fire('نجح!', response.message, 'success');

                                    // تحديث الواجهة
                                    updateQuestionStatusUI(questionId, newStatus);
                                } else {
                                    Swal.fire('خطأ!', response.message || 'حدث خطأ أثناء العملية', 'error');
                                }
                            },
                            error: function(xhr) {
                                console.error('Error:', xhr);
                                Swal.fire('خطأ!', 'حدث خطأ أثناء العملية', 'error');
                            }
                        });
                    }
                });
            }

            // دالة تحديث واجهة المستخدم بعد تغيير حالة السؤال
            function updateQuestionStatusUI(questionId, newStatus) {
                const card = $(`.saved-question-card[data-question-id="${questionId}"]`);
                const statusBadge = card.find('.status-badge');
                const toggleBtn = card.find('.toggle-question-status-btn');

                // تحديث badge الحالة
                if (newStatus == 1) {
                    statusBadge.removeClass('bg-warning').addClass('bg-success').text('مفعل');
                } else {
                    statusBadge.removeClass('bg-success').addClass('bg-warning').text('غير مفعل');
                }

                // تحديث زر التفعيل
                if (newStatus == 1) {
                    toggleBtn.removeClass('btn-outline-success').addClass('btn-outline-warning')
                        .attr('data-current-status', '1')
                        .html('<i class="fas fa-times"></i> إلغاء التفعيل');
                } else {
                    toggleBtn.removeClass('btn-outline-warning').addClass('btn-outline-success')
                        .attr('data-current-status', '0')
                        .html('<i class="fas fa-check"></i> تفعيل السؤال');
                }
            }

            });
            }, 1000);
        </script>
    @endpush

    <!-- المودالات - في نهاية الصفحة -->
    @push('modals')
        <!-- Add/Edit Question Modal -->
        <div class="modal fade" id="questionModal" tabindex="-1" aria-labelledby="questionModalLabel"
            aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="questionModalLabel">{{ __('Add New Question') }}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <form id="questionForm" method="POST">
                        @csrf
                        <input type="hidden" name="content_id" value="{{ $data->id }}">
                        <input type="hidden" name="question_id" id="question_id">
                        <div class="modal-body">
                            <div class="mb-3">
                                <label for="question" class="form-label">{{ __('Question') }} <span
                                        class="text-danger">*</span></label>
                                <textarea class="form-control" id="question" name="question" rows="2" required></textarea>
                            </div>
                            <div class="mb-3">
                                <label for="answer" class="form-label">{{ __('Answer') }} <span
                                        class="text-danger">*</span></label>
                                <textarea class="form-control" id="answer" name="answer" rows="3" required></textarea>
                            </div>
                            <div class="mb-3">
                                <label for="difficulty" class="form-label">{{ __('Difficulty') }}</label>
                                <select class="form-select" id="difficulty" name="difficulty">
                                    <option value="سهل">{{ __('Easy') }}</option>
                                    <option value="متوسط" selected>{{ __('Medium') }}</option>
                                    <option value="صعب">{{ __('Hard') }}</option>
                                </select>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary"
                                data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                            <button type="submit" class="btn btn-primary">{{ __('Save Question') }}</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Delete Confirmation Modal -->
        <div class="modal fade" id="deleteModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">{{ __('Confirm Deletion') }}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        {{ __('Are you sure you want to delete this question? This action cannot be undone.') }}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary"
                            data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                        <button type="button" class="btn btn-danger" id="confirmDelete">{{ __('Delete') }}</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- مودال إضافة سؤال يدوي -->
        <div class="modal fade" id="addManualQuestionModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">إضافة سؤال يدوي</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">السؤال:</label>
                            <textarea class="form-control" id="manualQuestion" rows="3" placeholder="اكتب السؤال هنا..."></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الإجابة:</label>
                            <div class="input-group">
                                <textarea class="form-control" id="manualAnswer" rows="4"
                                    placeholder="اكتب الإجابة هنا أو استخدم الذكاء الاصطناعي..."></textarea>
                                <a href="javascript:void(0)" class="btn btn-outline-info" id="aiAnswerBtn">
                                    <i class="fas fa-robot"></i> إجابة بالذكاء الاصطناعي
                                </a>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">الصعوبة:</label>
                                <select class="form-select" id="manualDifficulty">
                                    <option value="سهل">سهل</option>
                                    <option value="متوسط" selected>متوسط</option>
                                    <option value="صعب">صعب</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">النوع:</label>
                                <input type="text" class="form-control" id="manualType" value="عام"
                                    placeholder="نوع السؤال">
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-success" id="saveManualQuestionBtn">
                            <i class="fas fa-save"></i> حفظ السؤال
                        </button>
                    </div>
                </div>
            </div>
        </div>
    @endpush
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            document.addEventListener('click', function(e) {
                if (e.target.id === 'addManualQuestionBtn') {
                    e.preventDefault();
                    e.stopPropagation();
                    const modal = new bootstrap.Modal(document.getElementById('addManualQuestionModal'));
                    modal.show();
                }

                if (e.target.classList.contains('edit-saved-question-btn')) {
                    e.preventDefault();
                    e.stopPropagation();
                    const questionId = e.target.dataset.questionId;
                    const questionText = e.target.dataset.questionText;
                    const answerText = e.target.dataset.answerText;
                    const difficulty = e.target.dataset.difficulty;
                    const type = e.target.dataset.type;

                    showEditModal(questionId, questionText, answerText, difficulty, type);
                }

                if (e.target.classList.contains('delete-saved-question-btn')) {
                    e.preventDefault();
                    e.stopPropagation();
                    const questionId = e.target.dataset.questionId;
                    deleteSavedQuestion(questionId);
                }

                if (e.target.id === 'saveManualQuestionBtn') {
                    e.preventDefault();
                    e.stopPropagation();
                    saveManualQuestion();
                }

                if (e.target.id === 'aiAnswerBtn') {
                    e.preventDefault();
                    e.stopPropagation();
                    generateAIAnswer();
                }
            });
        });

        function showEditModal(id, questionText, answerText, difficulty, type) {
            const modalHtml = `
                <div class="modal fade" id="editQuestionModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">تعديل السؤال</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-3">
                                    <label class="form-label">السؤال:</label>
                                    <textarea class="form-control" id="editQuestion" rows="3">${questionText}</textarea>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">الإجابة:</label>
                                    <div class="input-group">
                                        <textarea class="form-control" id="editAnswer" rows="4">${answerText}</textarea>
                                        <button type="button" class="btn btn-outline-info" onclick="generateAIAnswerForEdit()">
                                            <i class="fas fa-robot"></i> إجابة بالذكاء الاصطناعي
                                        </button>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <label class="form-label">الصعوبة:</label>
                                        <select class="form-select" id="editDifficulty">
                                            <option value="سهل" ${difficulty === 'سهل' ? 'selected' : ''}>سهل</option>
                                            <option value="متوسط" ${difficulty === 'متوسط' ? 'selected' : ''}>متوسط</option>
                                            <option value="صعب" ${difficulty === 'صعب' ? 'selected' : ''}>صعب</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">النوع:</label>
                                        <input type="text" class="form-control" id="editType" value="${type}">
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                <button type="button" class="btn btn-success" onclick="updateQuestion(${id})">حفظ التعديل</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            const existingModal = document.getElementById('editQuestionModal');
            if (existingModal) existingModal.remove();

            document.body.insertAdjacentHTML('beforeend', modalHtml);
            const modal = new bootstrap.Modal(document.getElementById('editQuestionModal'));
            modal.show();
        }

        function updateQuestion(questionId) {
            const question = document.getElementById('editQuestion').value;
            const answer = document.getElementById('editAnswer').value;
            const difficulty = document.getElementById('editDifficulty').value;
            const type = document.getElementById('editType').value;

            fetch('/ai-questions/update', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    },
                    body: JSON.stringify({
                        question_id: questionId,
                        question: question,
                        answer: answer,
                        difficulty: difficulty,
                        type: type
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        bootstrap.Modal.getInstance(document.getElementById('editQuestionModal')).hide();
                        Swal.fire('تم!', 'تم تحديث السؤال بنجاح', 'success').then(() => {
                            location.reload();
                        });
                    } else {
                        Swal.fire('خطأ', data.message, 'error');
                    }
                })
                .catch(() => {
                    Swal.fire('خطأ', 'حدث خطأ في الاتصال', 'error');
                });
        }

        function deleteSavedQuestion(questionId) {
            Swal.fire({
                title: 'تأكيد الحذف',
                text: 'هل أنت متأكد من حذف هذا السؤال؟',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'نعم، احذف',
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    fetch('/ai-questions/delete', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                            },
                            body: JSON.stringify({
                                question_id: questionId
                            })
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                Swal.fire('تم الحذف!', 'تم حذف السؤال بنجاح', 'success').then(() => {
                                    location.reload();
                                });
                            } else {
                                Swal.fire('خطأ', data.message, 'error');
                            }
                        })
                        .catch(() => {
                            Swal.fire('خطأ', 'حدث خطأ في الاتصال', 'error');
                        });
                }
            });
        }

        function saveManualQuestion() {
            const question = document.getElementById('manualQuestion').value.trim();
            const answer = document.getElementById('manualAnswer').value.trim();
            const difficulty = document.getElementById('manualDifficulty').value;
            const type = document.getElementById('manualType').value.trim();

            if (!question || !answer) {
                Swal.fire('حقول مطلوبة', 'يرجى ملء السؤال والإجابة', 'warning');
                return;
            }

            fetch('{{ route('teacher.add.question') }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    },
                    body: JSON.stringify({
                        content_id: {{ $data->id }},
                        question: question,
                        answer: answer,
                        difficulty: difficulty,
                        type: type,
                        is_ai_generated: false
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        bootstrap.Modal.getInstance(document.getElementById('addManualQuestionModal')).hide();
                        Swal.fire('تم الحفظ!', 'تم إضافة السؤال بنجاح', 'success').then(() => {
                            location.reload();
                        });
                    } else {
                        Swal.fire('خطأ', data.message, 'error');
                    }
                })
                .catch(() => {
                    Swal.fire('خطأ', 'حدث خطأ في الاتصال', 'error');
                });
        }

        function generateAIAnswer() {
            const question = document.getElementById('manualQuestion').value.trim();
            if (!question) {
                Swal.fire('سؤال مطلوب', 'يرجى كتابة السؤال أولاً', 'warning');
                return;
            }

            const btn = document.getElementById('aiAnswerBtn');
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التوليد...';
            btn.disabled = true;

            fetch('{{ route('generate.ai.answer') }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    },
                    body: JSON.stringify({
                        question: question,
                        content_id: {{ $data->id }}
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('manualAnswer').value = data.answer;
                        Swal.fire('تم!', 'تم توليد الإجابة بنجاح', 'success');
                    } else {
                        Swal.fire('خطأ', data.message, 'error');
                    }
                })
                .catch(() => {
                    Swal.fire('خطأ', 'حدث خطأ في الاتصال', 'error');
                })
                .finally(() => {
                    btn.innerHTML = originalText;
                    btn.disabled = false;
                });
        }

        function generateAIAnswerForEdit() {
            const question = document.getElementById('editQuestion').value.trim();
            if (!question) {
                Swal.fire('سؤال مطلوب', 'يرجى كتابة السؤال أولاً', 'warning');
                return;
            }

            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التوليد...';
            btn.disabled = true;

            fetch('{{ route('generate.ai.answer') }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    },
                    body: JSON.stringify({
                        question: question,
                        content_id: {{ $data->id }}
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('editAnswer').value = data.answer;
                        Swal.fire('تم!', 'تم توليد الإجابة بنجاح', 'success');
                    } else {
                        Swal.fire('خطأ', data.message, 'error');
                    }
                })
                .catch(() => {
                    Swal.fire('خطأ', 'حدث خطأ في الاتصال', 'error');
                })
                .finally(() => {
                    btn.innerHTML = originalText;
                    btn.disabled = false;
                });
        }
    </script>
</x-app-layout>
