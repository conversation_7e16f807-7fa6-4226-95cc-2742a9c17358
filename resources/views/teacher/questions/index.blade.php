@extends('layouts.app')

@section('title', 'أسئلتي')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">أسئلتي</h3>
                </div>

                <div class="card-body">
                    <div id="questionsContainer">
                        <!-- سيتم تحميل الأسئلة هنا عبر JavaScript -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal لإضافة سؤال جديد -->
<div class="modal fade" id="addQuestionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة سؤال جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addQuestionForm">
                    <div class="mb-3">
                        <label class="form-label">المحتوى</label>
                        <select class="form-select" name="content_id" required>
                            <option value="">اختر المحتوى</option>
                            <!-- سيتم تحميل المحتويات هنا -->
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">السؤال</label>
                        <textarea class="form-control" name="question" rows="3" required></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الإجابة</label>
                        <textarea class="form-control" name="answer" rows="4"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">مستوى الصعوبة</label>
                        <select class="form-select" name="difficulty">
                            <option value="سهل">سهل</option>
                            <option value="متوسط" selected>متوسط</option>
                            <option value="صعب">صعب</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="submitQuestion()">إضافة السؤال</button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
// تحميل الأسئلة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadQuestions();
});

// تحميل الأسئلة
function loadQuestions(contentId = null) {
    const url = contentId ? `/ai-questions/${contentId}` : '/ai-questions/1'; // استخدم content_id مناسب
    
    fetch(url)
        .then(response => response.json())
        .then(data => {
            if (data.questions) {
                displayQuestions(data.questions);
            }
        })
        .catch(error => {
            console.error('Error loading questions:', error);
        });
}

// عرض الأسئلة
function displayQuestions(questions) {
    const container = document.getElementById('questionsContainer');
    
    if (questions.length === 0) {
        container.innerHTML = '<div class="alert alert-info">لا توجد أسئلة حالياً</div>';
        return;
    }
    
    let html = '<div class="row">';
    
    questions.forEach(question => {
        html += `
            <div class="col-md-6 mb-3">
                <div class="card ${question.status ? 'border-success' : 'border-warning'}">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div>
                            ${question.status ? 
                                '<span class="badge bg-success">مفعل - مرئي للجميع</span>' : 
                                '<span class="badge bg-warning">غير مفعل - مرئي لك فقط</span>'
                            }
                            ${question.is_own_question ? '<span class="badge bg-info ms-2">سؤالك</span>' : ''}
                        </div>
                        <div>
                            ${question.can_request_activation ? 
                                '<button class="btn btn-sm btn-outline-primary" onclick="requestActivation(' + question.id + ')">طلب التفعيل</button>' : 
                                ''
                            }
                        </div>
                    </div>
                    <div class="card-body">
                        <h6 class="card-title">${question.question}</h6>
                        ${question.answer ? 
                            '<p class="card-text"><strong>الإجابة:</strong> ' + question.answer + '</p>' : 
                            '<p class="card-text text-muted">لا توجد إجابة</p>'
                        }
                        <small class="text-muted">
                            الصعوبة: ${question.difficulty} | 
                            النوع: ${question.is_ai_generated ? 'ذكاء اصطناعي' : 'يدوي'}
                        </small>
                    </div>
                </div>
            </div>
        `;
    });
    
    html += '</div>';
    container.innerHTML = html;
}

// طلب تفعيل السؤال
function requestActivation(questionId) {
    if (confirm('هل تريد إرسال طلب تفعيل هذا السؤال للإدارة؟')) {
        fetch('/ai-questions/request-activation', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: JSON.stringify({ question_id: questionId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                loadQuestions(); // إعادة تحميل الأسئلة
            } else {
                alert(data.error || 'حدث خطأ أثناء إرسال الطلب');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء إرسال الطلب');
        });
    }
}

// إضافة سؤال جديد
function submitQuestion() {
    const form = document.getElementById('addQuestionForm');
    const formData = new FormData(form);
    
    const data = {
        content_id: formData.get('content_id'),
        question: formData.get('question'),
        answer: formData.get('answer'),
        difficulty: formData.get('difficulty')
    };
    
    fetch('/ai-questions/add', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('تم إضافة السؤال بنجاح. السؤال غير مفعل حالياً ومرئي لك فقط.');
            form.reset();
            bootstrap.Modal.getInstance(document.getElementById('addQuestionModal')).hide();
            loadQuestions(); // إعادة تحميل الأسئلة
        } else {
            alert(data.error || 'حدث خطأ أثناء إضافة السؤال');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء إضافة السؤال');
    });
}
</script>
@endsection
