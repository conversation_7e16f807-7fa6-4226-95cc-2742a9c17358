<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // تحديث الأسئلة الموجودة التي لا تحتوي على user_id
        // سنضع user_id = 1 (الأدمن) للأسئلة الموجودة مسبقاً
        DB::table('questions_answers')
            ->whereNull('user_id')
            ->update([
                'user_id' => 1, // الأدمن
                'status' => 1,  // مفعلة بشكل افتراضي
                'updated_at' => now(),
            ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // إعادة تعيين user_id إلى null للأسئلة التي تم تحديثها
        DB::table('questions_answers')
            ->where('user_id', 1)
            ->update([
                'user_id' => null,
                'updated_at' => now(),
            ]);
    }
};
