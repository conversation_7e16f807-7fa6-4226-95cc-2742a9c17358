<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('ai_usage_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('teacher_id')->constrained('users')->onDelete('cascade');
            $table->string('action'); // 'add_question', 'generate_question', etc.
            $table->date('usage_date');
            $table->integer('count')->default(1);
            $table->timestamps();
            
            $table->unique(['teacher_id', 'action', 'usage_date']);
            $table->index(['teacher_id', 'usage_date']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('ai_usage_logs');
    }
};